<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义渐变色 -->
    <linearGradient id="lambdaGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2196F3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#64B5F6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="stateGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF9800;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFB74D;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="mutationGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#81C784;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="crossoverGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9C27B0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#BA68C8;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1200" height="900" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#333">
    ANFDE主循环 - 前半部分 (景观状态分析 → 变异交叉)
  </text>
  
  <!-- 输入：初始种群 -->
  <g id="input-population">
    <rect x="50" y="60" width="1100" height="60" rx="10" fill="#E3F2FD" stroke="#1976D2" stroke-width="2"/>
    <text x="600" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#1976D2">
      输入：高质量初始种群 (来自初始化框架)
    </text>
    
    <!-- 种群示意 -->
    <g id="population-boxes">
      <rect x="200" y="95" width="80" height="20" rx="3" fill="rgba(255,255,255,0.4)" stroke="#1976D2" stroke-width="1"/>
      <circle cx="210" cy="105" r="2" fill="#1976D2"/>
      <circle cx="220" cy="105" r="2" fill="#1976D2"/>
      <circle cx="230" cy="105" r="2" fill="#1976D2"/>
      <circle cx="240" cy="105" r="2" fill="#1976D2"/>
      <circle cx="250" cy="105" r="2" fill="#1976D2"/>
      <circle cx="260" cy="105" r="2" fill="#1976D2"/>
      <text x="240" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#1976D2">个体1</text>
      
      <rect x="320" y="95" width="80" height="20" rx="3" fill="rgba(255,255,255,0.4)" stroke="#1976D2" stroke-width="1"/>
      <circle cx="330" cy="105" r="2" fill="#1976D2"/>
      <circle cx="340" cy="105" r="2" fill="#1976D2"/>
      <circle cx="350" cy="105" r="2" fill="#1976D2"/>
      <circle cx="360" cy="105" r="2" fill="#1976D2"/>
      <circle cx="370" cy="105" r="2" fill="#1976D2"/>
      <circle cx="380" cy="105" r="2" fill="#1976D2"/>
      <text x="360" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#1976D2">个体2</text>
      
      <text x="450" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#1976D2">...</text>
      
      <rect x="500" y="95" width="80" height="20" rx="3" fill="rgba(255,255,255,0.4)" stroke="#1976D2" stroke-width="1"/>
      <circle cx="510" cy="105" r="2" fill="#1976D2"/>
      <circle cx="520" cy="105" r="2" fill="#1976D2"/>
      <circle cx="530" cy="105" r="2" fill="#1976D2"/>
      <circle cx="540" cy="105" r="2" fill="#1976D2"/>
      <circle cx="550" cy="105" r="2" fill="#1976D2"/>
      <circle cx="560" cy="105" r="2" fill="#1976D2"/>
      <text x="540" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#1976D2">个体pop</text>
    </g>
  </g>
  
  <!-- 箭头 -->
  <path d="M 600 140 L 600 160" stroke="#333" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <!-- 第一步：景观状态分析 -->
  <g id="landscape-analysis">
    <rect x="50" y="180" width="1100" height="120" rx="10" fill="url(#lambdaGradient)" stroke="#2196F3" stroke-width="2"/>
    <text x="600" y="205" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
      步骤1: 景观状态分析 - 计算λ值
    </text>
    
    <!-- λ计算过程 -->
    <g id="lambda-calculation">
      <rect x="80" y="220" width="300" height="60" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
      <text x="230" y="240" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        λ值计算公式
      </text>
      <text x="90" y="260" font-family="Arial, sans-serif" font-size="11" fill="white">
        λ = (d_g - d_min) / (d_max - d_min)
      </text>
      <text x="90" y="275" font-family="Arial, sans-serif" font-size="10" fill="white">
        d_g: 最优个体平均距离
      </text>
      
      <!-- 距离矩阵示意 -->
      <rect x="420" y="220" width="200" height="60" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
      <text x="520" y="240" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        距离矩阵计算
      </text>
      <text x="430" y="260" font-family="Arial, sans-serif" font-size="10" fill="white">
        PDI(S1,S2) = |S1△S2| / |S1∪S2|
      </text>
      <text x="430" y="275" font-family="Arial, sans-serif" font-size="10" fill="white">
        向量化批量计算
      </text>
      
      <!-- λ值范围 -->
      <rect x="660" y="220" width="200" height="60" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
      <text x="760" y="240" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        λ值范围
      </text>
      <text x="670" y="260" font-family="Arial, sans-serif" font-size="10" fill="white">
        λ ∈ [0, 1]
      </text>
      <text x="670" y="275" font-family="Arial, sans-serif" font-size="10" fill="white">
        反映种群多样性状态
      </text>
    </g>
  </g>
  
  <!-- 箭头 -->
  <path d="M 600 310 L 600 330" stroke="#333" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <!-- 第二步：状态判断 -->
  <g id="state-determination">
    <rect x="50" y="350" width="1100" height="140" rx="10" fill="url(#stateGradient)" stroke="#FF9800" stroke-width="2"/>
    <text x="600" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
      步骤2: 自适应状态判断 - 动态阈值划分
    </text>
    
    <!-- 四种状态 -->
    <g id="four-states">
      <!-- 收敛状态 -->
      <rect x="80" y="390" width="200" height="80" rx="5" fill="#F18F01" stroke="white" stroke-width="2"/>
      <text x="180" y="410" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        收敛状态
      </text>
      <text x="90" y="430" font-family="Arial, sans-serif" font-size="10" fill="white">
        λ ∈ [0, Q1]
      </text>
      <text x="90" y="445" font-family="Arial, sans-serif" font-size="10" fill="white">
        种群聚集，多样性低
      </text>
      <text x="90" y="460" font-family="Arial, sans-serif" font-size="10" fill="white">
        使用DE/best/1变异
      </text>
      
      <!-- 开发状态 -->
      <rect x="300" y="390" width="200" height="80" rx="5" fill="#C73E1D" stroke="white" stroke-width="2"/>
      <text x="400" y="410" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        开发状态
      </text>
      <text x="310" y="430" font-family="Arial, sans-serif" font-size="10" fill="white">
        λ ∈ [Q1, (Q1+Q3)/2]
      </text>
      <text x="310" y="445" font-family="Arial, sans-serif" font-size="10" fill="white">
        局部搜索，精细优化
      </text>
      <text x="310" y="460" font-family="Arial, sans-serif" font-size="10" fill="white">
        使用DE/current-to-best/1
      </text>
      
      <!-- 探索状态 -->
      <rect x="520" y="390" width="200" height="80" rx="5" fill="#2E86AB" stroke="white" stroke-width="2"/>
      <text x="620" y="410" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        探索状态
      </text>
      <text x="530" y="430" font-family="Arial, sans-serif" font-size="10" fill="white">
        λ ∈ [(Q1+Q3)/2, Q3]
      </text>
      <text x="530" y="445" font-family="Arial, sans-serif" font-size="10" fill="white">
        全局搜索，增加多样性
      </text>
      <text x="530" y="460" font-family="Arial, sans-serif" font-size="10" fill="white">
        使用DE/rand/2变异
      </text>
      
      <!-- 逃逸状态 -->
      <rect x="740" y="390" width="200" height="80" rx="5" fill="#A23B72" stroke="white" stroke-width="2"/>
      <text x="840" y="410" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        逃逸状态
      </text>
      <text x="750" y="430" font-family="Arial, sans-serif" font-size="10" fill="white">
        λ ∈ [Q3, 1] 或停滞
      </text>
      <text x="750" y="445" font-family="Arial, sans-serif" font-size="10" fill="white">
        跳出局部最优
      </text>
      <text x="750" y="460" font-family="Arial, sans-serif" font-size="10" fill="white">
        使用逃逸候选池变异
      </text>
    </g>
  </g>
  
  <!-- 箭头 -->
  <path d="M 600 500 L 600 520" stroke="#333" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <!-- 第三步：变异操作 -->
  <g id="mutation-operations">
    <rect x="50" y="540" width="1100" height="160" rx="10" fill="url(#mutationGradient)" stroke="#4CAF50" stroke-width="2"/>
    <text x="600" y="565" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
      步骤3: 状态感知变异操作
    </text>
    
    <!-- 变异过程示意 -->
    <g id="mutation-process">
      <!-- 原个体 -->
      <g id="original-individual">
        <text x="150" y="590" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
          原个体 X_i
        </text>
        <rect x="100" y="600" width="100" height="25" rx="3" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="1"/>
        <circle cx="110" cy="612" r="3" fill="white"/>
        <circle cx="125" cy="612" r="3" fill="white"/>
        <circle cx="140" cy="612" r="3" fill="white"/>
        <circle cx="155" cy="612" r="3" fill="white"/>
        <circle cx="170" cy="612" r="3" fill="white"/>
        <circle cx="185" cy="612" r="3" fill="white"/>
      </g>
      
      <!-- 变异箭头 -->
      <path d="M 220 612 L 280 612" stroke="white" stroke-width="3" marker-end="url(#whitearrow)"/>
      <text x="250" y="605" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
        状态感知变异
      </text>
      
      <!-- 变异个体 -->
      <g id="mutant-individual">
        <text x="380" y="590" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
          变异个体 V_i
        </text>
        <rect x="330" y="600" width="100" height="25" rx="3" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="1"/>
        <circle cx="340" cy="612" r="3" fill="white"/>
        <circle cx="355" cy="612" r="3" fill="#FFD54F"/>  <!-- 变异节点用黄色 -->
        <circle cx="370" cy="612" r="3" fill="white"/>
        <circle cx="385" cy="612" r="3" fill="#FFD54F"/>  <!-- 变异节点用黄色 -->
        <circle cx="400" cy="612" r="3" fill="white"/>
        <circle cx="415" cy="612" r="3" fill="white"/>
      </g>
      
      <!-- 参数生成 -->
      <g id="parameter-generation">
        <rect x="500" y="580" width="200" height="60" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
        <text x="600" y="600" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
          自适应参数生成
        </text>
        <text x="510" y="620" font-family="Arial, sans-serif" font-size="10" fill="white">
          CR ~ N(μ_CR, 0.1²)
        </text>
        <text x="510" y="635" font-family="Arial, sans-serif" font-size="10" fill="white">
          F ~ Cauchy(μ_F, 0.1)
        </text>
      </g>
      
      <!-- 变异策略说明 -->
      <g id="mutation-strategies">
        <rect x="750" y="580" width="300" height="60" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
        <text x="900" y="600" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
          变异策略选择
        </text>
        <text x="760" y="620" font-family="Arial, sans-serif" font-size="10" fill="white">
          • 收敛: DE/best/1  • 开发: DE/current-to-best/1
        </text>
        <text x="760" y="635" font-family="Arial, sans-serif" font-size="10" fill="white">
          • 探索: DE/rand/2  • 逃逸: 候选池引导变异
        </text>
      </g>
    </g>
    
    <!-- 变异详细过程 -->
    <g id="mutation-details">
      <text x="150" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
        差分向量计算
      </text>
      <text x="150" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
        V = X_base + F×(X_r1 - X_r2)
      </text>
      
      <text x="400" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
        节点替换策略
      </text>
      <text x="400" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
        基于LFV值优先替换
      </text>
      
      <text x="650" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
        重复修复
      </text>
      <text x="650" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
        确保解的有效性
      </text>
      
      <text x="900" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
        批量处理
      </text>
      <text x="900" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
        并行变异整个种群
      </text>
    </g>
  </g>
  
  <!-- 箭头 -->
  <path d="M 600 710 L 600 730" stroke="#333" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <!-- 第四步：交叉操作 -->
  <g id="crossover-operations">
    <rect x="50" y="750" width="1100" height="120" rx="10" fill="url(#crossoverGradient)" stroke="#9C27B0" stroke-width="2"/>
    <text x="600" y="775" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
      步骤4: 二项式交叉操作
    </text>
    
    <!-- 交叉过程示意 -->
    <g id="crossover-process">
      <!-- 目标个体 -->
      <g id="target-individual">
        <text x="200" y="800" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
          目标个体 X_i
        </text>
        <rect x="150" y="810" width="100" height="25" rx="3" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="1"/>
        <circle cx="160" cy="822" r="3" fill="white"/>
        <circle cx="175" cy="822" r="3" fill="white"/>
        <circle cx="190" cy="822" r="3" fill="white"/>
        <circle cx="205" cy="822" r="3" fill="white"/>
        <circle cx="220" cy="822" r="3" fill="white"/>
        <circle cx="235" cy="822" r="3" fill="white"/>
      </g>
      
      <!-- 变异个体 -->
      <g id="mutant-for-crossover">
        <text x="400" y="800" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
          变异个体 V_i
        </text>
        <rect x="350" y="810" width="100" height="25" rx="3" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="1"/>
        <circle cx="360" cy="822" r="3" fill="white"/>
        <circle cx="375" cy="822" r="3" fill="#FFD54F"/>
        <circle cx="390" cy="822" r="3" fill="white"/>
        <circle cx="405" cy="822" r="3" fill="#FFD54F"/>
        <circle cx="420" cy="822" r="3" fill="white"/>
        <circle cx="435" cy="822" r="3" fill="white"/>
      </g>
      
      <!-- 交叉箭头 -->
      <path d="M 270 822 L 330 822" stroke="white" stroke-width="2" marker-end="url(#whitearrow)"/>
      <path d="M 470 822 L 530 822" stroke="white" stroke-width="2" marker-end="url(#whitearrow)"/>
      <text x="300" y="815" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">CR</text>
      <text x="500" y="815" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">交叉</text>
      
      <!-- 试验个体 -->
      <g id="trial-individual">
        <text x="650" y="800" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
          试验个体 U_i
        </text>
        <rect x="600" y="810" width="100" height="25" rx="3" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="1"/>
        <circle cx="610" cy="822" r="3" fill="white"/>      <!-- 来自目标 -->
        <circle cx="625" cy="822" r="3" fill="#FFD54F"/>    <!-- 来自变异 -->
        <circle cx="640" cy="822" r="3" fill="white"/>      <!-- 来自目标 -->
        <circle cx="655" cy="822" r="3" fill="#FFD54F"/>    <!-- 来自变异 -->
        <circle cx="670" cy="822" r="3" fill="white"/>      <!-- 来自目标 -->
        <circle cx="685" cy="822" r="3" fill="white"/>      <!-- 来自目标 -->
      </g>
      
      <!-- 交叉公式 -->
      <g id="crossover-formula">
        <rect x="750" y="800" width="300" height="50" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
        <text x="900" y="820" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
          交叉公式
        </text>
        <text x="760" y="840" font-family="Arial, sans-serif" font-size="10" fill="white">
          U_i[j] = V_i[j] if rand() &lt; CR, else X_i[j]
        </text>
      </g>
    </g>
  </g>
  
  <!-- 输出箭头 -->
  <path d="M 600 880 L 600 900" stroke="#333" stroke-width="3" marker-end="url(#arrowhead)"/>
  <text x="600" y="895" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">
    继续 → 选择操作 + 局部搜索 + 种群更新 (下半部分)
  </text>
  
  <!-- 定义箭头标记 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
     refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <marker id="whitearrow" markerWidth="10" markerHeight="7" 
     refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="white" />
    </marker>
  </defs>
</svg>
