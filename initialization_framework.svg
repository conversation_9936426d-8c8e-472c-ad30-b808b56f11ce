<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义渐变色 -->
    <linearGradient id="lhsGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#81C784;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="scoreGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2196F3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#64B5F6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cubeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF9800;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFB74D;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 背景 -->
  <rect width="1200" height="800" fill="#f8f9fa"/>

  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#333">
    初始化操作框架图
  </text>

  <!-- 第一阶段：网络预处理 -->
  <g id="preprocessing">
    <rect x="50" y="70" width="300" height="120" rx="10" fill="#E3F2FD" stroke="#1976D2" stroke-width="2"/>
    <text x="200" y="95" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#1976D2">
      网络预处理阶段
    </text>

    <!-- 网络图节点 -->
    <circle cx="120" cy="130" r="8" fill="#333"/>
    <circle cx="140" cy="120" r="8" fill="#333"/>
    <circle cx="160" cy="140" r="8" fill="#333"/>
    <circle cx="180" cy="125" r="8" fill="#333"/>
    <circle cx="200" cy="135" r="8" fill="#333"/>
    <circle cx="220" cy="115" r="8" fill="#333"/>
    <circle cx="240" cy="145" r="8" fill="#333"/>
    <circle cx="260" cy="125" r="8" fill="#333"/>
    <circle cx="280" cy="135" r="8" fill="#333"/>

    <!-- 连接线 -->
    <line x1="120" y1="130" x2="140" y2="120" stroke="#666" stroke-width="1"/>
    <line x1="140" y1="120" x2="160" y2="140" stroke="#666" stroke-width="1"/>
    <line x1="160" y1="140" x2="180" y2="125" stroke="#666" stroke-width="1"/>
    <line x1="180" y1="125" x2="200" y2="135" stroke="#666" stroke-width="1"/>
    <line x1="200" y1="135" x2="220" y2="115" stroke="#666" stroke-width="1"/>
    <line x1="220" y1="115" x2="240" y2="145" stroke="#666" stroke-width="1"/>
    <line x1="240" y1="145" x2="260" y2="125" stroke="#666" stroke-width="1"/>
    <line x1="260" y1="125" x2="280" y2="135" stroke="#666" stroke-width="1"/>

    <text x="200" y="170" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#666">
      • 桥节点检测 • 综合中心性计算 • 直径路径划分
    </text>
  </g>

  <!-- 箭头1 -->
  <path d="M 200 200 L 200 230" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>

  <!-- 第二阶段：拉丁超立方采样 -->
  <g id="lhs-sampling">
    <rect x="50" y="250" width="300" height="180" rx="10" fill="url(#lhsGradient)" stroke="#4CAF50" stroke-width="2"/>
    <text x="200" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
      拉丁超立方采样 (LHS)
    </text>

    <!-- 动态维度立方体表示LHS采样空间 -->
    <g id="lhs-cube">
      <!-- 立方体前面 -->
      <rect x="120" y="300" width="60" height="60" fill="url(#cubeGradient)" stroke="#FF5722" stroke-width="2"/>
      <!-- 立方体顶面 -->
      <polygon points="120,300 140,280 200,280 180,300" fill="#FFB74D" stroke="#FF5722" stroke-width="2"/>
      <!-- 立方体右面 -->
      <polygon points="180,300 200,280 200,340 180,360" fill="#FF8A65" stroke="#FF5722" stroke-width="2"/>

      <!-- 采样点对应区域 -->
      <circle cx="135" cy="315" r="3" fill="#D32F2F"/>
      <text x="135" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">R0</text>

      <circle cx="155" cy="335" r="3" fill="#D32F2F"/>
      <text x="155" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">R1</text>

      <circle cx="165" cy="320" r="3" fill="#D32F2F"/>
      <text x="175" y="315" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">R2</text>

      <text x="200" y="390" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
        effective_dims = min(k, 区域数)
      </text>
    </g>

    <!-- 区域划分示意 -->
    <g id="regions">
      <text x="250" y="300" font-family="Arial, sans-serif" font-size="11" fill="white">维度1: R0 (直径路径)</text>
      <text x="250" y="315" font-family="Arial, sans-serif" font-size="11" fill="white">维度2: R1 (距离=1)</text>
      <text x="250" y="330" font-family="Arial, sans-serif" font-size="11" fill="white">维度3: R2 (距离=2)</text>
      <text x="250" y="345" font-family="Arial, sans-serif" font-size="11" fill="white">...</text>
      <text x="250" y="365" font-family="Arial, sans-serif" font-size="10" fill="white">LHS矩阵: n×effective_dims</text>
    </g>
  </g>

  <!-- 第三阶段：度中心性采样 -->
  <g id="degree-sampling">
    <rect x="450" y="250" width="300" height="180" rx="10" fill="url(#scoreGradient)" stroke="#2196F3" stroke-width="2"/>
    <text x="600" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
      度中心性采样 + 扰动
    </text>

    <!-- 度排序示意 -->
    <g id="degree-ranking">
      <circle cx="500" cy="310" r="12" fill="#FFF" stroke="#2196F3" stroke-width="2"/>
      <text x="500" y="315" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2196F3">8</text>

      <circle cx="530" cy="320" r="10" fill="#FFF" stroke="#2196F3" stroke-width="2"/>
      <text x="530" y="325" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2196F3">6</text>

      <circle cx="560" cy="330" r="8" fill="#FFF" stroke="#2196F3" stroke-width="2"/>
      <text x="560" y="335" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2196F3">4</text>

      <circle cx="590" cy="340" r="6" fill="#FFF" stroke="#2196F3" stroke-width="2"/>
      <text x="590" y="345" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2196F3">3</text>

      <circle cx="620" cy="350" r="4" fill="#FFF" stroke="#2196F3" stroke-width="2"/>
      <text x="620" y="355" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2196F3">2</text>

      <text x="600" y="380" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
        按度值排序 + 50%概率扰动
      </text>
    </g>
  </g>

  <!-- 箭头2和3 -->
  <path d="M 200 440 L 200 470" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 600 440 L 600 470" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>

  <!-- 第四阶段：混合初始化 -->
  <g id="hybrid-initialization">
    <rect x="150" y="490" width="500" height="250" rx="10" fill="#F3E5F5" stroke="#9C27B0" stroke-width="2"/>
    <text x="400" y="515" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#9C27B0">
      混合初始化种群构建
    </text>

    <!-- 阶段0：适应度缓存 -->
    <rect x="170" y="530" width="120" height="40" rx="5" fill="#E1F5FE" stroke="#0277BD" stroke-width="1"/>
    <text x="230" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#0277BD">
      阶段0: 适应度缓存
    </text>

    <!-- 阶段1：质量筛选 -->
    <rect x="310" y="530" width="120" height="40" rx="5" fill="#E8F5E8" stroke="#388E3C" stroke-width="1"/>
    <text x="370" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#388E3C">
      阶段1: 质量筛选
    </text>

    <!-- 阶段2：多样性筛选 -->
    <rect x="450" y="530" width="120" height="40" rx="5" fill="#FFF3E0" stroke="#F57C00" stroke-width="1"/>
    <text x="510" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#F57C00">
      阶段2: 多样性筛选
    </text>

    <!-- 筛选过程示意 -->
    <g id="filtering-process">
      <!-- LHS解集 -->
      <g id="lhs-solutions">
        <circle cx="200" cy="600" r="6" fill="#4CAF50"/>
        <circle cx="220" cy="590" r="6" fill="#4CAF50"/>
        <circle cx="240" cy="610" r="6" fill="#4CAF50"/>
        <circle cx="260" cy="595" r="6" fill="#4CAF50"/>
        <circle cx="280" cy="605" r="6" fill="#4CAF50"/>
        <text x="240" y="630" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#4CAF50">
          LHS解集
        </text>
      </g>

      <!-- 评分解集 -->
      <g id="score-solutions">
        <circle cx="450" cy="600" r="6" fill="#2196F3"/>
        <circle cx="470" cy="590" r="6" fill="#2196F3"/>
        <circle cx="490" cy="610" r="6" fill="#2196F3"/>
        <circle cx="510" cy="595" r="6" fill="#2196F3"/>
        <circle cx="530" cy="605" r="6" fill="#2196F3"/>
        <text x="490" y="630" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2196F3">
          评分解集
        </text>
      </g>

      <!-- 筛选箭头 -->
      <path d="M 300 600 L 340 600" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
      <path d="M 430 600 L 390 600" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>

      <!-- 最终种群 -->
      <g id="final-population">
        <circle cx="360" cy="590" r="6" fill="#9C27B0"/>
        <circle cx="380" cy="600" r="6" fill="#9C27B0"/>
        <circle cx="400" cy="590" r="6" fill="#9C27B0"/>
        <circle cx="420" cy="600" r="6" fill="#9C27B0"/>
        <text x="390" y="620" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#9C27B0">
          初始种群
        </text>
      </g>
    </g>

    <!-- 筛选策略说明 -->
    <text x="400" y="660" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#666">
      质量筛选: 保留适应度最高的50% | 多样性筛选: 相似度阈值0.8
    </text>

    <!-- 补充策略 -->
    <text x="400" y="680" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#666">
      补充策略: 区域轮询 → 桥节点 → 外围节点
    </text>

    <!-- 最终处理 -->
    <text x="400" y="700" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#666">
      最终处理: 节点整数化 → 去重 → 种群大小调整
    </text>
  </g>

  <!-- 定义箭头标记 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7"
     refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>

  <!-- 关键思想说明 -->
  <g id="key-ideas">
    <rect x="800" y="100" width="350" height="600" rx="10" fill="#FAFAFA" stroke="#666" stroke-width="1"/>
    <text x="975" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#333">
      初始化核心思想
    </text>

    <!-- 思想1 -->
    <rect x="820" y="150" width="310" height="80" rx="5" fill="#E3F2FD" stroke="#1976D2" stroke-width="1"/>
    <text x="975" y="175" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#1976D2">
      1. 空间覆盖策略
    </text>
    <text x="830" y="195" font-family="Arial, sans-serif" font-size="11" fill="#333">
      • LHS确保解空间均匀覆盖
    </text>
    <text x="830" y="210" font-family="Arial, sans-serif" font-size="11" fill="#333">
      • 基于网络直径的区域划分
    </text>
    <text x="830" y="225" font-family="Arial, sans-serif" font-size="11" fill="#333">
      • 避免解集中在局部区域
    </text>

    <!-- 思想2 -->
    <rect x="820" y="250" width="310" height="80" rx="5" fill="#E8F5E8" stroke="#388E3C" stroke-width="1"/>
    <text x="975" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#388E3C">
      2. 质量与多样性平衡
    </text>
    <text x="830" y="295" font-family="Arial, sans-serif" font-size="11" fill="#333">
      • 度中心性保证解的质量
    </text>
    <text x="830" y="310" font-family="Arial, sans-serif" font-size="11" fill="#333">
      • 扰动机制增加多样性
    </text>
    <text x="830" y="325" font-family="Arial, sans-serif" font-size="11" fill="#333">
      • 相似度阈值控制冗余
    </text>

    <!-- 思想3 -->
    <rect x="820" y="350" width="310" height="80" rx="5" fill="#FFF3E0" stroke="#F57C00" stroke-width="1"/>
    <text x="975" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#F57C00">
      3. 分层筛选机制
    </text>
    <text x="830" y="395" font-family="Arial, sans-serif" font-size="11" fill="#333">
      • 适应度预计算提高效率
    </text>
    <text x="830" y="410" font-family="Arial, sans-serif" font-size="11" fill="#333">
      • 质量优先，多样性补充
    </text>
    <text x="830" y="425" font-family="Arial, sans-serif" font-size="11" fill="#333">
      • 三阶段补充策略
    </text>

    <!-- 思想4 -->
    <rect x="820" y="450" width="310" height="80" rx="5" fill="#F3E5F5" stroke="#9C27B0" stroke-width="1"/>
    <text x="975" y="475" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#9C27B0">
      4. 网络结构感知
    </text>
    <text x="830" y="495" font-family="Arial, sans-serif" font-size="11" fill="#333">
      • 桥节点识别关键位置
    </text>
    <text x="830" y="510" font-family="Arial, sans-serif" font-size="11" fill="#333">
      • 综合中心性评估节点重要性
    </text>
    <text x="830" y="525" font-family="Arial, sans-serif" font-size="11" fill="#333">
      • 结构洞理论指导选择
    </text>

    <!-- LHS采样示例 -->
    <rect x="820" y="550" width="310" height="150" rx="5" fill="#FFEBEE" stroke="#D32F2F" stroke-width="1"/>
    <text x="975" y="575" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#D32F2F">
      LHS采样示例 (k=3, 3个区域)
    </text>

    <!-- LHS矩阵示例 -->
    <text x="830" y="595" font-family="Arial, sans-serif" font-size="10" fill="#333">
      LHS矩阵 (5×3):    R0    R1    R2
    </text>
    <text x="830" y="610" font-family="Arial, sans-serif" font-size="9" fill="#333">
      解1: [0.2, 0.6, 0.1] → {2, 9, 11}
    </text>
    <text x="830" y="625" font-family="Arial, sans-serif" font-size="9" fill="#333">
      解2: [0.8, 0.3, 0.7] → {5, 7, 18}
    </text>
    <text x="830" y="640" font-family="Arial, sans-serif" font-size="9" fill="#333">
      解3: [0.5, 0.9, 0.4] → {3, 10, 15}
    </text>
    <text x="830" y="655" font-family="Arial, sans-serif" font-size="9" fill="#333">
      解4: [0.1, 0.2, 0.8] → {1, 7, 19}
    </text>
    <text x="830" y="670" font-family="Arial, sans-serif" font-size="9" fill="#333">
      解5: [0.7, 0.4, 0.6] → {4, 8, 17}
    </text>

    <text x="975" y="690" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">
      维度数 = min(k=3, 区域数=3) = 3维立方体
    </text>
  </g>
</svg>
