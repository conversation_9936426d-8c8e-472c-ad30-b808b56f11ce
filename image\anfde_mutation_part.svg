<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 创新渐变和图案 -->
    <radialGradient id="mutationCore" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#81C784;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#2E7D32;stop-opacity:1" />
    </radialGradient>
    
    <linearGradient id="dnaHelix" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF6B6B;stop-opacity:1" />
      <stop offset="25%" style="stop-color:#4ECDC4;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#45B7D1;stop-opacity:1" />
      <stop offset="75%" style="stop-color:#96CEB4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFEAA7;stop-opacity:1" />
    </linearGradient>
    
    <!-- 基因螺旋图案 -->
    <pattern id="genePattern" patternUnits="userSpaceOnUse" width="50" height="30">
      <rect width="50" height="30" fill="#4CAF50"/>
      <path d="M0,15 Q12.5,5 25,15 Q37.5,25 50,15" stroke="#81C784" stroke-width="2" fill="none"/>
      <path d="M0,15 Q12.5,25 25,15 Q37.5,5 50,15" stroke="#A5D6A7" stroke-width="2" fill="none"/>
      <circle cx="12.5" cy="10" r="2" fill="#FFD54F"/>
      <circle cx="37.5" cy="20" r="2" fill="#FF5722"/>
    </pattern>
    
    <!-- 发光效果 -->
    <filter id="bioGlow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- 变异粒子效果 -->
    <filter id="mutationParticles">
      <feGaussianBlur stdDeviation="1" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1400" height="800" fill="#0a0a0a"/>
  
  <!-- 标题 -->
  <text x="700" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="22" font-weight="bold" fill="#4CAF50" filter="url(#bioGlow)">
    🧬 状态感知变异工厂 - 智能基因重组引擎
  </text>
  
  <!-- 输入：状态信息 -->
  <g id="state-input">
    <rect x="50" y="60" width="1300" height="80" rx="15" fill="rgba(76,175,80,0.1)" stroke="#4CAF50" stroke-width="2"/>
    <text x="700" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#81C784">
      📡 输入状态信息: current_state + 更新参数 (μ_CR, μ_F)
    </text>
    
    <!-- 状态指示器 -->
    <g id="state-indicators">
      <rect x="200" y="105" width="100" height="25" rx="5" fill="#2E86AB" stroke="white" stroke-width="1"/>
      <text x="250" y="122" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">探索状态</text>
      
      <rect x="350" y="105" width="120" height="25" rx="5" fill="rgba(255,255,255,0.2)" stroke="#4CAF50" stroke-width="1"/>
      <text x="410" y="122" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#4CAF50">μ_CR = 0.7</text>
      
      <rect x="520" y="105" width="120" height="25" rx="5" fill="rgba(255,255,255,0.2)" stroke="#4CAF50" stroke-width="1"/>
      <text x="580" y="122" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#4CAF50">μ_F = 0.5</text>
    </g>
  </g>
  
  <!-- 数据流箭头 -->
  <path d="M 700 150 Q 720 170 700 190" stroke="#4CAF50" stroke-width="4" fill="none" marker-end="url(#mutationFlow)" filter="url(#bioGlow)"/>
  
  <!-- 变异策略选择器 -->
  <g id="mutation-strategy-selector">
    <rect x="50" y="210" width="1300" height="140" rx="15" fill="url(#genePattern)" stroke="#4CAF50" stroke-width="3"/>
    <text x="700" y="235" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="white">
      🎯 智能策略选择器 - 状态驱动DE变异
    </text>
    
    <!-- 策略选择逻辑 -->
    <g id="strategy-logic">
      <!-- 中央控制器 -->
      <circle cx="700" cy="290" r="40" fill="url(#mutationCore)" stroke="#FFD700" stroke-width="3" filter="url(#bioGlow)"/>
      <text x="700" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        策略核心
      </text>
      
      <!-- 四个策略分支 -->
      <!-- 收敛策略 -->
      <g id="convergence-strategy">
        <rect x="150" y="260" width="150" height="60" rx="10" fill="#F18F01" stroke="white" stroke-width="2"/>
        <text x="225" y="280" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
          🎯 DE/best/1
        </text>
        <text x="160" y="300" font-family="Arial, sans-serif" font-size="10" fill="white">
          V = X_best + F×(X_r1 - X_r2)
        </text>
        <text x="160" y="315" font-family="Arial, sans-serif" font-size="9" fill="white">
          精英引导，快速收敛
        </text>
      </g>
      
      <!-- 开发策略 -->
      <g id="exploitation-strategy">
        <rect x="350" y="260" width="150" height="60" rx="10" fill="#C73E1D" stroke="white" stroke-width="2"/>
        <text x="425" y="280" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">
          ⚡ DE/current-to-best/1
        </text>
        <text x="360" y="300" font-family="Arial, sans-serif" font-size="9" fill="white">
          V = X_i + F×(X_best - X_i) + F×(X_r1 - X_r2)
        </text>
        <text x="360" y="315" font-family="Arial, sans-serif" font-size="9" fill="white">
          局部优化，平衡搜索
        </text>
      </g>
      
      <!-- 探索策略 -->
      <g id="exploration-strategy">
        <rect x="750" y="260" width="150" height="60" rx="10" fill="#2E86AB" stroke="white" stroke-width="2" filter="url(#bioGlow)"/>
        <text x="825" y="280" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
          🌟 DE/rand/2
        </text>
        <text x="760" y="300" font-family="Arial, sans-serif" font-size="9" fill="white">
          V = X_r1 + F×(X_r2 - X_r3) + F×(X_r4 - X_r5)
        </text>
        <text x="760" y="315" font-family="Arial, sans-serif" font-size="9" fill="white">
          随机探索，增强多样性
        </text>
      </g>
      
      <!-- 逃逸策略 -->
      <g id="escape-strategy">
        <rect x="950" y="260" width="150" height="60" rx="10" fill="#A23B72" stroke="white" stroke-width="2"/>
        <text x="1025" y="280" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
          🚀 候选池变异
        </text>
        <text x="960" y="300" font-family="Arial, sans-serif" font-size="9" fill="white">
          从逃逸候选池选择
        </text>
        <text x="960" y="315" font-family="Arial, sans-serif" font-size="9" fill="white">
          跳出局部最优
        </text>
      </g>
      
      <!-- 连接线 -->
      <path d="M 660 290 L 300 290" stroke="#FFD700" stroke-width="3" fill="none" opacity="0.7"/>
      <path d="M 660 290 L 500 290" stroke="#FFD700" stroke-width="3" fill="none" opacity="0.7"/>
      <path d="M 740 290 L 900 290" stroke="#FFD700" stroke-width="3" fill="none" opacity="0.7" filter="url(#bioGlow)"/>
      <path d="M 740 290 L 1100 290" stroke="#FFD700" stroke-width="3" fill="none" opacity="0.7"/>
    </g>
  </g>
  
  <!-- 数据流箭头 -->
  <path d="M 700 360 Q 720 380 700 400" stroke="#4CAF50" stroke-width="4" fill="none" marker-end="url(#mutationFlow)" filter="url(#bioGlow)"/>
  
  <!-- 变异执行工厂 -->
  <g id="mutation-execution-factory">
    <rect x="50" y="420" width="1300" height="200" rx="15" fill="url(#dnaHelix)" stroke="#FF6B6B" stroke-width="3"/>
    <text x="700" y="445" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="white">
      🏭 变异执行工厂 - 并行基因重组流水线
    </text>
    
    <!-- 变异流水线 -->
    <g id="mutation-pipeline">
      <!-- 输入个体 -->
      <g id="input-individual">
        <text x="150" y="475" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">原始个体 X_i</text>
        <rect x="100" y="485" width="100" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="2"/>
        <circle cx="115" cy="500" r="4" fill="#4CAF50"/>
        <circle cx="130" cy="500" r="4" fill="#2196F3"/>
        <circle cx="145" cy="500" r="4" fill="#FF5722"/>
        <circle cx="160" cy="500" r="4" fill="#9C27B0"/>
        <circle cx="175" cy="500" r="4" fill="#FF9800"/>
        <circle cx="185" cy="500" r="4" fill="#607D8B"/>
      </g>
      
      <!-- 变异处理器 -->
      <g id="mutation-processor">
        <rect x="280" y="470" width="120" height="80" rx="10" fill="rgba(255,255,255,0.1)" stroke="#4CAF50" stroke-width="3"/>
        <text x="340" y="490" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
          ⚙️ 变异引擎
        </text>
        
        <!-- 齿轮效果 -->
        <circle cx="320" cy="520" r="15" fill="rgba(255,255,255,0.2)" stroke="#4CAF50" stroke-width="2"/>
        <circle cx="360" cy="520" r="15" fill="rgba(255,255,255,0.2)" stroke="#4CAF50" stroke-width="2"/>
        <polygon points="320,510 325,515 320,530 315,515" fill="#4CAF50"/>
        <polygon points="360,510 365,515 360,530 355,515" fill="#4CAF50"/>
        
        <text x="340" y="545" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">DE/rand/2</text>
      </g>
      
      <!-- 参数注入器 -->
      <g id="parameter-injector">
        <rect x="450" y="470" width="100" height="80" rx="10" fill="rgba(255,215,0,0.2)" stroke="#FFD700" stroke-width="2"/>
        <text x="500" y="490" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">
          💉 参数注入
        </text>
        
        <!-- 注射器效果 -->
        <rect x="480" y="505" width="40" height="8" rx="2" fill="#FFD700"/>
        <circle cx="485" cy="509" r="3" fill="white"/>
        <text x="500" y="525" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">F = 0.5</text>
        <text x="500" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">CR = 0.7</text>
      </g>
      
      <!-- LFV优化器 -->
      <g id="lfv-optimizer">
        <rect x="600" y="470" width="120" height="80" rx="10" fill="rgba(255,255,255,0.1)" stroke="#96CEB4" stroke-width="2"/>
        <text x="660" y="490" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">
          🎯 LFV优化器
        </text>
        <text x="610" y="510" font-family="Arial, sans-serif" font-size="9" fill="white">
          基于LFV值排序
        </text>
        <text x="610" y="525" font-family="Arial, sans-serif" font-size="9" fill="white">
          优先替换低价值节点
        </text>
        <text x="610" y="540" font-family="Arial, sans-serif" font-size="9" fill="white">
          保持解的质量
        </text>
      </g>
      
      <!-- 重复修复器 -->
      <g id="duplicate-fixer">
        <rect x="770" y="470" width="100" height="80" rx="10" fill="rgba(255,255,255,0.1)" stroke="#FF5722" stroke-width="2"/>
        <text x="820" y="490" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">
          🔧 去重修复
        </text>
        <text x="780" y="510" font-family="Arial, sans-serif" font-size="9" fill="white">
          检测重复节点
        </text>
        <text x="780" y="525" font-family="Arial, sans-serif" font-size="9" fill="white">
          随机替换重复
        </text>
        <text x="780" y="540" font-family="Arial, sans-serif" font-size="9" fill="white">
          确保解有效性
        </text>
      </g>
      
      <!-- 输出变异个体 -->
      <g id="output-mutant">
        <text x="1000" y="475" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">变异个体 V_i</text>
        <rect x="950" y="485" width="100" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="2"/>
        <circle cx="965" cy="500" r="4" fill="#4CAF50"/>  <!-- 保持 -->
        <circle cx="980" cy="500" r="4" fill="#FFD54F" filter="url(#mutationParticles)"/>  <!-- 变异 -->
        <circle cx="995" cy="500" r="4" fill="#FF5722"/>  <!-- 保持 -->
        <circle cx="1010" cy="500" r="4" fill="#FFD54F" filter="url(#mutationParticles)"/>  <!-- 变异 -->
        <circle cx="1025" cy="500" r="4" fill="#9C27B0"/>  <!-- 保持 -->
        <circle cx="1035" cy="500" r="4" fill="#FFD54F" filter="url(#mutationParticles)"/>  <!-- 变异 -->
      </g>
      
      <!-- 流水线箭头 -->
      <path d="M 220 500 L 260 500" stroke="#4CAF50" stroke-width="3" marker-end="url(#mutationFlow)"/>
      <path d="M 420 500 L 430 500" stroke="#4CAF50" stroke-width="3" marker-end="url(#mutationFlow)"/>
      <path d="M 570 500 L 580 500" stroke="#4CAF50" stroke-width="3" marker-end="url(#mutationFlow)"/>
      <path d="M 740 500 L 750 500" stroke="#4CAF50" stroke-width="3" marker-end="url(#mutationFlow)"/>
      <path d="M 890 500 L 930 500" stroke="#4CAF50" stroke-width="3" marker-end="url(#mutationFlow)"/>
    </g>
    
    <!-- 批量处理指示器 -->
    <g id="batch-indicator">
      <rect x="100" y="570" width="1200" height="30" rx="5" fill="rgba(76,175,80,0.2)" stroke="#4CAF50" stroke-width="1"/>
      <text x="700" y="590" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#4CAF50">
        🚀 并行处理: 同时对整个种群进行变异操作 (pop_size个个体)
      </text>
    </g>
  </g>
  
  <!-- 输出箭头 -->
  <path d="M 700 630 Q 720 650 700 670" stroke="#4CAF50" stroke-width="4" fill="none" marker-end="url(#mutationFlow)" filter="url(#bioGlow)"/>
  <text x="700" y="690" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#4CAF50">
    🧬 输出变异种群 → 进入交叉融合阶段
  </text>
  
  <!-- 变异统计信息 -->
  <g id="mutation-stats">
    <rect x="50" y="720" width="1300" height="60" rx="10" fill="rgba(76,175,80,0.1)" stroke="#4CAF50" stroke-width="2"/>
    <text x="700" y="745" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#4CAF50">
      📊 变异统计: 当前使用DE/rand/2策略 | 变异率: 85% | 成功保留高质量基因
    </text>
    <text x="700" y="765" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#81C784">
      🔬 LFV引导替换 | 🛡️ 重复检测修复 | ⚡ 向量化并行处理 | 🎯 状态自适应
    </text>
  </g>
  
  <!-- 定义箭头标记 -->
  <defs>
    <marker id="mutationFlow" markerWidth="12" markerHeight="8" 
     refX="10" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#4CAF50" />
    </marker>
  </defs>
</svg>
