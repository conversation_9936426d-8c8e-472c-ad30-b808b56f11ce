<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 创新渐变和图案 -->
    <radialGradient id="neuralGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#E3F2FD;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#2196F3;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#0D47A1;stop-opacity:1" />
    </radialGradient>
    
    <linearGradient id="brainWave" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#FF6B6B;stop-opacity:1" />
      <stop offset="25%" style="stop-color:#4ECDC4;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#45B7D1;stop-opacity:1" />
      <stop offset="75%" style="stop-color:#96CEB4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFEAA7;stop-opacity:1" />
    </linearGradient>
    
    <!-- 蜂窝图案 -->
    <pattern id="hexPattern" patternUnits="userSpaceOnUse" width="30" height="26">
      <rect width="30" height="26" fill="#2196F3"/>
      <polygon points="15,2 25,8 25,18 15,24 5,18 5,8" fill="none" stroke="#64B5F6" stroke-width="1"/>
    </pattern>
    
    <!-- 电路板图案 -->
    <pattern id="circuitPattern" patternUnits="userSpaceOnUse" width="40" height="40">
      <rect width="40" height="40" fill="#9C27B0"/>
      <circle cx="10" cy="10" r="2" fill="#E1BEE7"/>
      <circle cx="30" cy="30" r="2" fill="#E1BEE7"/>
      <line x1="10" y1="10" x2="30" y2="10" stroke="#BA68C8" stroke-width="1"/>
      <line x1="30" y1="10" x2="30" y2="30" stroke="#BA68C8" stroke-width="1"/>
    </pattern>
    
    <!-- 发光效果 -->
    <filter id="neonGlow">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- 脉冲动画效果 -->
    <filter id="pulse">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1400" height="1000" fill="#0a0a0a"/>
  
  <!-- 标题 -->
  <text x="700" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#00E5FF" filter="url(#neonGlow)">
    🧠 ANFDE神经进化系统 - 智能景观感知引擎
  </text>
  
  <!-- 输入层：种群神经网络 -->
  <g id="population-neural-network">
    <rect x="50" y="60" width="1300" height="100" rx="20" fill="url(#hexPattern)" stroke="#00E5FF" stroke-width="3"/>
    <text x="700" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="white">
      🔗 种群神经网络层 - 分布式个体节点
    </text>
    
    <!-- 神经元节点表示个体 -->
    <g id="neural-nodes">
      <!-- 神经元1 -->
      <g id="neuron1">
        <circle cx="200" cy="120" r="20" fill="url(#neuralGradient)" stroke="#00E5FF" stroke-width="2" filter="url(#pulse)"/>
        <circle cx="190" cy="115" r="3" fill="#FFD54F"/>
        <circle cx="200" cy="115" r="3" fill="#4CAF50"/>
        <circle cx="210" cy="115" r="3" fill="#FF5722"/>
        <circle cx="190" cy="125" r="3" fill="#9C27B0"/>
        <circle cx="200" cy="125" r="3" fill="#FF9800"/>
        <circle cx="210" cy="125" r="3" fill="#2196F3"/>
        <text x="200" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#00E5FF">神经元1</text>
      </g>
      
      <!-- 神经元2 -->
      <g id="neuron2">
        <circle cx="400" cy="120" r="20" fill="url(#neuralGradient)" stroke="#00E5FF" stroke-width="2" filter="url(#pulse)"/>
        <circle cx="390" cy="115" r="3" fill="#E91E63"/>
        <circle cx="400" cy="115" r="3" fill="#00BCD4"/>
        <circle cx="410" cy="115" r="3" fill="#8BC34A"/>
        <circle cx="390" cy="125" r="3" fill="#FFC107"/>
        <circle cx="400" cy="125" r="3" fill="#795548"/>
        <circle cx="410" cy="125" r="3" fill="#607D8B"/>
        <text x="400" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#00E5FF">神经元2</text>
      </g>
      
      <!-- 连接线 -->
      <path d="M 220 120 Q 310 100 380 120" stroke="#00E5FF" stroke-width="2" fill="none" opacity="0.6"/>
      
      <!-- 更多神经元 -->
      <text x="600" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#00E5FF">⚡ ⚡ ⚡</text>
      
      <!-- 神经元N -->
      <g id="neuronN">
        <circle cx="800" cy="120" r="20" fill="url(#neuralGradient)" stroke="#00E5FF" stroke-width="2" filter="url(#pulse)"/>
        <circle cx="790" cy="115" r="3" fill="#FF6B6B"/>
        <circle cx="800" cy="115" r="3" fill="#4ECDC4"/>
        <circle cx="810" cy="115" r="3" fill="#45B7D1"/>
        <circle cx="790" cy="125" r="3" fill="#96CEB4"/>
        <circle cx="800" cy="125" r="3" fill="#FFEAA7"/>
        <circle cx="810" cy="125" r="3" fill="#DDA0DD"/>
        <text x="800" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#00E5FF">神经元N</text>
      </g>
      
      <path d="M 620 120 Q 710 100 780 120" stroke="#00E5FF" stroke-width="2" fill="none" opacity="0.6"/>
    </g>
  </g>
  
  <!-- 数据流箭头 -->
  <path d="M 700 170 Q 720 190 700 210" stroke="#00E5FF" stroke-width="4" fill="none" marker-end="url(#dataFlow)" filter="url(#neonGlow)"/>
  
  <!-- λ值计算：量子计算模拟器 -->
  <g id="quantum-lambda-computer">
    <rect x="50" y="230" width="1300" height="160" rx="20" fill="url(#brainWave)" stroke="#FF6B6B" stroke-width="3"/>
    <text x="700" y="255" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="white">
      ⚛️ 量子景观分析器 - λ值多维计算矩阵
    </text>
    
    <!-- 量子比特表示距离矩阵 -->
    <g id="quantum-matrix">
      <!-- 量子门1：距离计算 -->
      <rect x="100" y="280" width="200" height="80" rx="15" fill="rgba(255,255,255,0.1)" stroke="#4ECDC4" stroke-width="2"/>
      <text x="200" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        🌀 量子门1: 距离矩阵
      </text>
      
      <!-- 量子比特可视化 -->
      <g id="qubits1">
        <circle cx="130" cy="320" r="8" fill="#4ECDC4" filter="url(#neonGlow)"/>
        <circle cx="150" cy="320" r="8" fill="#FF6B6B" filter="url(#neonGlow)"/>
        <circle cx="170" cy="320" r="8" fill="#96CEB4" filter="url(#neonGlow)"/>
        <circle cx="190" cy="320" r="8" fill="#FFEAA7" filter="url(#neonGlow)"/>
        
        <circle cx="130" cy="340" r="8" fill="#45B7D1" filter="url(#neonGlow)"/>
        <circle cx="150" cy="340" r="8" fill="#DDA0DD" filter="url(#neonGlow)"/>
        <circle cx="170" cy="340" r="8" fill="#FFB6C1" filter="url(#neonGlow)"/>
        <circle cx="190" cy="340" r="8" fill="#98FB98" filter="url(#neonGlow)"/>
      </g>
      <text x="200" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
        PDI(S1,S2) = |S1△S2|/|S1∪S2|
      </text>
      
      <!-- 量子门2：λ值计算 -->
      <rect x="350" y="280" width="200" height="80" rx="15" fill="rgba(255,255,255,0.1)" stroke="#45B7D1" stroke-width="2"/>
      <text x="450" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        🔮 量子门2: λ值合成
      </text>
      
      <!-- λ值量子态 -->
      <g id="lambda-quantum-state">
        <ellipse cx="450" cy="330" rx="40" ry="20" fill="rgba(255,215,0,0.3)" stroke="#FFD700" stroke-width="2" filter="url(#neonGlow)"/>
        <text x="450" y="335" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#FFD700">
          λ = 0.65
        </text>
      </g>
      <text x="450" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
        λ = (d_g - d_min) / (d_max - d_min)
      </text>
      
      <!-- 量子纠缠连接 -->
      <path d="M 300 320 Q 325 310 350 320" stroke="#00E5FF" stroke-width="3" fill="none" opacity="0.8"/>
      <path d="M 300 340 Q 325 350 350 340" stroke="#00E5FF" stroke-width="3" fill="none" opacity="0.8"/>
    </g>
    
    <!-- 适应度批处理器 -->
    <g id="fitness-processor">
      <rect x="600" y="280" width="250" height="80" rx="15" fill="rgba(255,255,255,0.1)" stroke="#96CEB4" stroke-width="2"/>
      <text x="725" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        🚀 并行适应度处理器
      </text>
      
      <!-- 处理器核心 -->
      <g id="processor-cores">
        <rect x="620" y="315" width="30" height="15" rx="3" fill="#96CEB4" opacity="0.8"/>
        <rect x="660" y="315" width="30" height="15" rx="3" fill="#FFEAA7" opacity="0.8"/>
        <rect x="700" y="315" width="30" height="15" rx="3" fill="#FF6B6B" opacity="0.8"/>
        <rect x="740" y="315" width="30" height="15" rx="3" fill="#4ECDC4" opacity="0.8"/>
        
        <rect x="620" y="340" width="30" height="15" rx="3" fill="#DDA0DD" opacity="0.8"/>
        <rect x="660" y="340" width="30" height="15" rx="3" fill="#45B7D1" opacity="0.8"/>
        <rect x="700" y="340" width="30" height="15" rx="3" fill="#98FB98" opacity="0.8"/>
        <rect x="740" y="340" width="30" height="15" rx="3" fill="#FFB6C1" opacity="0.8"/>
      </g>
      <text x="725" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
        fitness_batch(population)
      </text>
    </g>
    
    <!-- 历史记录器 -->
    <g id="history-recorder">
      <rect x="900" y="280" width="200" height="80" rx="15" fill="rgba(255,255,255,0.1)" stroke="#FFEAA7" stroke-width="2"/>
      <text x="1000" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        📊 时序记录器
      </text>
      
      <!-- 时间轴 -->
      <line x1="920" y1="330" x2="1080" y2="330" stroke="#FFEAA7" stroke-width="2"/>
      <circle cx="930" cy="330" r="3" fill="#FF6B6B"/>
      <circle cx="950" cy="325" r="3" fill="#4ECDC4"/>
      <circle cx="970" cy="335" r="3" fill="#96CEB4"/>
      <circle cx="990" cy="320" r="3" fill="#45B7D1"/>
      <circle cx="1010" cy="340" r="3" fill="#DDA0DD"/>
      <circle cx="1030" cy="325" r="3" fill="#FFEAA7"/>
      <circle cx="1050" cy="335" r="3" fill="#FFB6C1"/>
      <circle cx="1070" cy="330" r="3" fill="#98FB98"/>
      
      <text x="1000" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
        lambda_history.append(λ)
      </text>
    </g>
  </g>
  
  <!-- 数据流箭头 -->
  <path d="M 700 400 Q 720 420 700 440" stroke="#FF6B6B" stroke-width="4" fill="none" marker-end="url(#dataFlow)" filter="url(#neonGlow)"/>
  
  <!-- 状态判断：AI决策树 -->
  <g id="ai-decision-tree">
    <rect x="50" y="460" width="1300" height="200" rx="20" fill="url(#circuitPattern)" stroke="#9C27B0" stroke-width="3"/>
    <text x="700" y="485" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="white">
      🤖 AI状态决策树 - 智能阈值分析引擎
    </text>
    
    <!-- 决策节点 -->
    <g id="decision-nodes">
      <!-- 根节点：前10代检查 -->
      <g id="root-node">
        <polygon points="200,520 240,500 280,520 240,540" fill="#E1BEE7" stroke="white" stroke-width="2" filter="url(#neonGlow)"/>
        <text x="240" y="525" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#4A148C">
          gen &lt; 10?
        </text>
      </g>
      
      <!-- 分支1：强制探索 -->
      <rect x="100" y="570" width="120" height="40" rx="10" fill="#2E86AB" stroke="white" stroke-width="2"/>
      <text x="160" y="595" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">🔍 强制探索</text>
      
      <!-- 分支2：逃逸检测 -->
      <g id="escape-detection">
        <polygon points="400,560 440,540 480,560 440,580" fill="#E1BEE7" stroke="white" stroke-width="2" filter="url(#neonGlow)"/>
        <text x="440" y="565" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#4A148C">
          逃逸条件?
        </text>
      </g>
      
      <!-- 分支3：动态阈值计算 -->
      <g id="threshold-calculation">
        <polygon points="600,560 640,540 680,560 640,580" fill="#E1BEE7" stroke="white" stroke-width="2" filter="url(#neonGlow)"/>
        <text x="640" y="565" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#4A148C">
          计算Q1,Q3
        </text>
      </g>
      
      <!-- 状态输出节点 -->
      <g id="state-outputs">
        <rect x="800" y="520" width="80" height="30" rx="5" fill="#F18F01" stroke="white" stroke-width="1"/>
        <text x="840" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">收敛</text>
        
        <rect x="900" y="520" width="80" height="30" rx="5" fill="#C73E1D" stroke="white" stroke-width="1"/>
        <text x="940" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">开发</text>
        
        <rect x="1000" y="520" width="80" height="30" rx="5" fill="#2E86AB" stroke="white" stroke-width="1"/>
        <text x="1040" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">探索</text>
        
        <rect x="1100" y="520" width="80" height="30" rx="5" fill="#A23B72" stroke="white" stroke-width="1"/>
        <text x="1140" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">逃逸</text>
      </g>
      
      <!-- 连接线 -->
      <path d="M 220 540 L 160 570" stroke="#00E5FF" stroke-width="2" fill="none"/>
      <path d="M 260 540 L 420 540" stroke="#00E5FF" stroke-width="2" fill="none"/>
      <path d="M 460 540 L 620 540" stroke="#00E5FF" stroke-width="2" fill="none"/>
      <path d="M 660 540 L 800 535" stroke="#00E5FF" stroke-width="2" fill="none"/>
      <path d="M 680 540 L 900 535" stroke="#00E5FF" stroke-width="2" fill="none"/>
      <path d="M 680 540 L 1000 535" stroke="#00E5FF" stroke-width="2" fill="none"/>
      <path d="M 680 540 L 1100 535" stroke="#00E5FF" stroke-width="2" fill="none"/>
    </g>
    
    <!-- 阈值光谱 -->
    <g id="threshold-spectrum">
      <rect x="200" y="620" width="800" height="20" fill="url(#brainWave)" rx="10" stroke="white" stroke-width="1"/>
      <line x1="350" y1="610" x2="350" y2="650" stroke="white" stroke-width="2"/>
      <text x="350" y="605" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">Q1</text>
      
      <line x1="600" y1="610" x2="600" y2="650" stroke="white" stroke-width="2"/>
      <text x="600" y="605" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">(Q1+Q3)/2</text>
      
      <line x1="850" y1="610" x2="850" y2="650" stroke="white" stroke-width="2"/>
      <text x="850" y="605" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">Q3</text>
      
      <!-- 当前位置指示器 -->
      <polygon points="650,610 660,620 650,630 640,620" fill="#FFD700" stroke="white" stroke-width="1" filter="url(#neonGlow)"/>
    </g>
  </g>
  
  <!-- 数据流箭头 -->
  <path d="M 700 670 Q 720 690 700 710" stroke="#9C27B0" stroke-width="4" fill="none" marker-end="url(#dataFlow)" filter="url(#neonGlow)"/>
  
  <!-- 参数更新：自适应学习系统 -->
  <g id="adaptive-learning-system">
    <rect x="50" y="730" width="1300" height="120" rx="20" fill="linear-gradient(45deg, #667eea 0%, #764ba2 100%)" stroke="#667eea" stroke-width="3"/>
    <text x="700" y="755" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="white">
      🧠 自适应学习系统 - 参数进化引擎
    </text>
    
    <!-- 学习模块 -->
    <g id="learning-modules">
      <!-- 成功经验收集器 -->
      <rect x="100" y="780" width="200" height="50" rx="10" fill="rgba(255,255,255,0.1)" stroke="#4ECDC4" stroke-width="2"/>
      <text x="200" y="800" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        📚 经验收集器
      </text>
      <text x="110" y="820" font-family="Arial, sans-serif" font-size="10" fill="white">
        success_CR[], success_F[]
      </text>
      
      <!-- 参数更新器 -->
      <rect x="350" y="780" width="300" height="50" rx="10" fill="rgba(255,255,255,0.1)" stroke="#96CEB4" stroke-width="2"/>
      <text x="500" y="800" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        ⚙️ 参数进化器
      </text>
      <text x="360" y="820" font-family="Arial, sans-serif" font-size="10" fill="white">
        μ_CR = (1-c)×μ_CR + c×mean_weighted(success_CR)
      </text>
      
      <!-- 随机生成器 -->
      <rect x="700" y="780" width="200" height="50" rx="10" fill="rgba(255,255,255,0.1)" stroke="#FFEAA7" stroke-width="2"/>
      <text x="800" y="800" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        🎲 智能生成器
      </text>
      <text x="710" y="820" font-family="Arial, sans-serif" font-size="10" fill="white">
        CR~N(μ_CR,0.1²), F~Cauchy(μ_F,0.1)
      </text>
      
      <!-- 输出参数 -->
      <rect x="950" y="780" width="150" height="50" rx="10" fill="rgba(255,215,0,0.2)" stroke="#FFD700" stroke-width="2"/>
      <text x="1025" y="800" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        ✨ 新参数
      </text>
      <text x="960" y="820" font-family="Arial, sans-serif" font-size="10" fill="white">
        更新后的μ_CR, μ_F
      </text>
      
      <!-- 连接流 -->
      <path d="M 320 805 L 330 805" stroke="#00E5FF" stroke-width="3" marker-end="url(#dataFlow)"/>
      <path d="M 670 805 L 680 805" stroke="#00E5FF" stroke-width="3" marker-end="url(#dataFlow)"/>
      <path d="M 920 805 L 930 805" stroke="#00E5FF" stroke-width="3" marker-end="url(#dataFlow)"/>
    </g>
  </g>
  
  <!-- 输出箭头 -->
  <path d="M 700 860 Q 720 880 700 900" stroke="#667eea" stroke-width="4" fill="none" marker-end="url(#dataFlow)" filter="url(#neonGlow)"/>
  <text x="700" y="920" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#00E5FF">
    🚀 进入状态感知变异工厂 → 智能基因重组
  </text>
  
  <!-- 系统状态指示器 -->
  <g id="system-status">
    <rect x="50" y="940" width="1300" height="50" rx="15" fill="rgba(0,229,255,0.1)" stroke="#00E5FF" stroke-width="2"/>
    <text x="700" y="960" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#00E5FF">
      🔬 系统状态: 景观分析完成 | 当前状态: 探索模式 | λ=0.65 | 参数已更新
    </text>
    <text x="700" y="980" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#4ECDC4">
      ⚡ 向量化计算 | 🧠 自适应阈值 | 📊 历史学习 | 🎯 精准状态感知
    </text>
  </g>
  
  <!-- 定义箭头标记 -->
  <defs>
    <marker id="dataFlow" markerWidth="12" markerHeight="8" 
     refX="10" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#00E5FF" />
    </marker>
  </defs>
</svg>
