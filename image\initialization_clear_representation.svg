<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义渐变色 -->
    <linearGradient id="networkGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1976D2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#42A5F5;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="lhsGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#81C784;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="degreeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF9800;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFB74D;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="hybridGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9C27B0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#BA68C8;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cubeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF5722;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF8A65;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1200" height="800" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#333">
    初始化框架 - 清晰的节点与解表示 (k=10~100)
  </text>
  
  <!-- 第一层：网络预处理 -->
  <g id="network-preprocessing">
    <rect x="50" y="60" width="1100" height="80" rx="10" fill="url(#networkGradient)" stroke="#1976D2" stroke-width="2"/>
    <text x="600" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
      网络预处理：直径路径划分 + 桥节点检测 + 综合中心性计算
    </text>
    
    <!-- 网络节点示意 - 用小圆圈表示单个节点 -->
    <g id="network-nodes">
      <circle cx="100" cy="110" r="3" fill="white"/>
      <circle cx="115" cy="105" r="3" fill="white"/>
      <circle cx="130" cy="115" r="3" fill="white"/>
      <circle cx="145" cy="110" r="3" fill="white"/>
      <circle cx="160" cy="105" r="3" fill="white"/>
      <circle cx="175" cy="115" r="3" fill="white"/>
      <circle cx="190" cy="110" r="3" fill="white"/>
      <text x="145" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">网络节点</text>
    </g>
    
    <!-- 区域划分结果 -->
    <g id="region-result">
      <rect x="250" y="95" width="60" height="20" rx="3" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="1"/>
      <text x="280" y="108" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">R0</text>
      <rect x="320" y="95" width="60" height="20" rx="3" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="1"/>
      <text x="350" y="108" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">R1</text>
      <rect x="390" y="95" width="60" height="20" rx="3" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="1"/>
      <text x="420" y="108" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">R2</text>
      <text x="460" y="108" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">...</text>
      <rect x="480" y="95" width="60" height="20" rx="3" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="1"/>
      <text x="510" y="108" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">Rn</text>
    </g>
    
    <!-- 桥节点 -->
    <g id="bridge-nodes">
      <circle cx="600" cy="105" r="5" fill="#FFD54F" stroke="white" stroke-width="2"/>
      <circle cx="620" cy="110" r="5" fill="#FFD54F" stroke="white" stroke-width="2"/>
      <circle cx="640" cy="105" r="5" fill="#FFD54F" stroke="white" stroke-width="2"/>
      <text x="620" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">桥节点</text>
    </g>
    
    <!-- 综合评分 -->
    <g id="combined-scores">
      <rect x="750" y="95" width="150" height="20" rx="3" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="1"/>
      <text x="825" y="108" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">综合中心性评分</text>
    </g>
  </g>
  
  <!-- 箭头 -->
  <path d="M 600 150 L 600 170" stroke="#333" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <!-- 第二层：双路径采样 -->
  <g id="dual-sampling">
    <!-- LHS采样路径 -->
    <g id="lhs-path">
      <rect x="50" y="190" width="500" height="200" rx="10" fill="url(#lhsGradient)" stroke="#4CAF50" stroke-width="2"/>
      <text x="300" y="215" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
        路径1: 高维LHS采样 (effective_dims ≤ k)
      </text>
      
      <!-- 高维立方体表示 -->
      <g id="high-dim-cube">
        <!-- 多层立方体表示高维 -->
        <rect x="120" y="240" width="50" height="50" fill="url(#cubeGradient)" stroke="#D32F2F" stroke-width="2" opacity="1"/>
        <rect x="130" y="230" width="50" height="50" fill="url(#cubeGradient)" stroke="#D32F2F" stroke-width="2" opacity="0.8"/>
        <rect x="140" y="220" width="50" height="50" fill="url(#cubeGradient)" stroke="#D32F2F" stroke-width="2" opacity="0.6"/>
        <rect x="150" y="210" width="50" height="50" fill="url(#cubeGradient)" stroke="#D32F2F" stroke-width="2" opacity="0.4"/>
        <rect x="160" y="200" width="50" height="50" fill="url(#cubeGradient)" stroke="#D32F2F" stroke-width="2" opacity="0.2"/>
        
        <!-- 采样点 -->
        <circle cx="145" cy="265" r="2" fill="white"/>
        <circle cx="155" cy="255" r="2" fill="white"/>
        <circle cx="165" cy="245" r="2" fill="white"/>
        <circle cx="175" cy="235" r="2" fill="white"/>
        <circle cx="185" cy="225" r="2" fill="white"/>
        
        <text x="160" y="310" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
          高维采样空间
        </text>
        <text x="160" y="325" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
          维度 = min(k, 区域数)
        </text>
      </g>
      
      <!-- LHS解表示 - 用矩形框表示解集合 -->
      <g id="lhs-solutions-representation">
        <text x="320" y="250" font-family="Arial, sans-serif" font-size="12" fill="white">
          LHS解示例 (k=5):
        </text>
        
        <!-- 解1 -->
        <rect x="320" y="260" width="120" height="20" rx="3" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="1"/>
        <text x="325" y="273" font-family="Arial, sans-serif" font-size="9" fill="white">解1: {3,12,25,87,45}</text>
        
        <!-- 解2 -->
        <rect x="320" y="285" width="120" height="20" rx="3" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="1"/>
        <text x="325" y="298" font-family="Arial, sans-serif" font-size="9" fill="white">解2: {7,18,33,92,61}</text>
        
        <!-- 解3 -->
        <rect x="320" y="310" width="120" height="20" rx="3" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="1"/>
        <text x="325" y="323" font-family="Arial, sans-serif" font-size="9" fill="white">解3: {1,29,41,76,53}</text>
        
        <text x="380" y="345" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
          每个解包含k个节点
        </text>
        <text x="380" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
          空间覆盖均匀
        </text>
      </g>
    </g>
    
    <!-- 度中心性采样路径 -->
    <g id="degree-path">
      <rect x="600" y="190" width="500" height="200" rx="10" fill="url(#degreeGradient)" stroke="#FF9800" stroke-width="2"/>
      <text x="850" y="215" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
        路径2: 度中心性采样 + 50%扰动
      </text>
      
      <!-- 度排序示意 - 用不同大小圆圈表示单个节点的度值 -->
      <g id="degree-ranking">
        <text x="630" y="240" font-family="Arial, sans-serif" font-size="11" fill="white">单个节点(按度排序):</text>
        
        <circle cx="650" cy="255" r="8" fill="white" stroke="#FF9800" stroke-width="2"/>
        <text x="650" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#FF9800">1</text>
        
        <circle cx="680" cy="255" r="6" fill="white" stroke="#FF9800" stroke-width="2"/>
        <text x="680" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="7" fill="#FF9800">2</text>
        
        <circle cx="710" cy="255" r="4" fill="white" stroke="#FF9800" stroke-width="2"/>
        <text x="710" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="6" fill="#FF9800">3</text>
        
        <circle cx="740" cy="255" r="3" fill="white" stroke="#FF9800" stroke-width="2"/>
        <text x="740" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="6" fill="#FF9800">4</text>
        
        <circle cx="770" cy="255" r="2" fill="white" stroke="#FF9800" stroke-width="2"/>
        <text x="770" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="5" fill="#FF9800">5</text>
        
        <text x="710" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">
          节点大小 ∝ 度值
        </text>
      </g>
      
      <!-- 度中心性解表示 - 用矩形框表示解集合 -->
      <g id="degree-solutions-representation">
        <text x="820" y="300" font-family="Arial, sans-serif" font-size="12" fill="white">
          度+扰动解示例 (k=5):
        </text>
        
        <!-- 解1 - 部分扰动 -->
        <rect x="820" y="310" width="140" height="20" rx="3" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="1"/>
        <text x="825" y="323" font-family="Arial, sans-serif" font-size="9" fill="white">解1: {1,15,3,23,5}</text>
        <circle cx="970" cy="320" r="3" fill="#4CAF50"/>
        <circle cx="980" cy="320" r="3" fill="#F44336"/>
        <circle cx="990" cy="320" r="3" fill="#4CAF50"/>
        
        <!-- 解2 - 部分扰动 -->
        <rect x="820" y="335" width="140" height="20" rx="3" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="1"/>
        <text x="825" y="348" font-family="Arial, sans-serif" font-size="9" fill="white">解2: {8,2,19,4,31}</text>
        <circle cx="970" cy="345" r="3" fill="#F44336"/>
        <circle cx="980" cy="345" r="3" fill="#4CAF50"/>
        <circle cx="990" cy="345" r="3" fill="#F44336"/>
        
        <!-- 解3 - 部分扰动 -->
        <rect x="820" y="360" width="140" height="20" rx="3" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="1"/>
        <text x="825" y="373" font-family="Arial, sans-serif" font-size="9" fill="white">解3: {1,2,27,14,5}</text>
        <circle cx="970" cy="370" r="3" fill="#4CAF50"/>
        <circle cx="980" cy="370" r="3" fill="#4CAF50"/>
        <circle cx="990" cy="370" r="3" fill="#F44336"/>
        
        <text x="890" y="390" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">
          绿色●:保持 红色●:扰动
        </text>
      </g>
    </g>
  </g>
  
  <!-- 箭头 -->
  <path d="M 300 400 L 300 430" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 850 400 L 850 430" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 575 430 L 625 430" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 第三层：混合初始化 -->
  <g id="hybrid-initialization">
    <rect x="50" y="450" width="1100" height="300" rx="10" fill="url(#hybridGradient)" stroke="#9C27B0" stroke-width="2"/>
    <text x="600" y="475" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
      混合初始化种群构建 - 分层筛选机制
    </text>
    
    <!-- 五个阶段 -->
    <g id="five-stages">
      <!-- 阶段0 -->
      <rect x="80" y="500" width="180" height="60" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
      <text x="170" y="520" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        阶段0: 适应度预计算
      </text>
      <text x="170" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
        并行缓存所有候选解
      </text>
      <text x="170" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
        的LIE_two_hop值
      </text>
      
      <!-- 阶段1 -->
      <rect x="280" y="500" width="180" height="60" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
      <text x="370" y="520" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        阶段1: 质量筛选
      </text>
      <text x="370" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
        保留适应度最高的50%
      </text>
      <text x="370" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
        LHS解 + 评分解
      </text>
      
      <!-- 阶段2 -->
      <rect x="480" y="500" width="180" height="60" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
      <text x="570" y="520" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        阶段2: 多样性筛选
      </text>
      <text x="570" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
        相似度阈值0.8
      </text>
      <text x="570" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
        去除冗余解
      </text>
      
      <!-- 阶段3 -->
      <rect x="680" y="500" width="180" height="60" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
      <text x="770" y="520" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        阶段3: 智能补充
      </text>
      <text x="770" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
        区域轮询→桥节点
      </text>
      <text x="770" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
        →外围节点
      </text>
      
      <!-- 阶段4 -->
      <rect x="880" y="500" width="180" height="60" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
      <text x="970" y="520" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        阶段4: 最终处理
      </text>
      <text x="970" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
        节点整数化+去重
      </text>
      <text x="970" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
        调整到目标大小
      </text>
    </g>
    
    <!-- 筛选流程示意 - 用矩形框表示解集合 -->
    <g id="filtering-flow">
      <!-- LHS解集 -->
      <g id="lhs-solutions">
        <rect x="120" y="590" width="80" height="15" rx="3" fill="#4CAF50" stroke="white" stroke-width="1"/>
        <rect x="120" y="610" width="80" height="15" rx="3" fill="#4CAF50" stroke="white" stroke-width="1"/>
        <rect x="120" y="630" width="80" height="15" rx="3" fill="#4CAF50" stroke="white" stroke-width="1"/>
        <text x="160" y="660" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
          LHS解集
        </text>
        <text x="160" y="675" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
          (SN/2个解)
        </text>
      </g>
      
      <!-- 评分解集 -->
      <g id="score-solutions">
        <rect x="320" y="590" width="80" height="15" rx="3" fill="#FF9800" stroke="white" stroke-width="1"/>
        <rect x="320" y="610" width="80" height="15" rx="3" fill="#FF9800" stroke="white" stroke-width="1"/>
        <rect x="320" y="630" width="80" height="15" rx="3" fill="#FF9800" stroke="white" stroke-width="1"/>
        <text x="360" y="660" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
          评分解集
        </text>
        <text x="360" y="675" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
          (SN/2个解)
        </text>
      </g>
      
      <!-- 筛选箭头 -->
      <path d="M 220 620 L 270 620" stroke="white" stroke-width="2" marker-end="url(#whitearrow)"/>
      <path d="M 420 620 L 470 620" stroke="white" stroke-width="2" marker-end="url(#whitearrow)"/>
      
      <!-- 筛选过程 -->
      <rect x="490" y="600" width="120" height="40" rx="5" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="1"/>
      <text x="550" y="625" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
        质量+多样性筛选
      </text>
      
      <!-- 最终种群 -->
      <g id="final-population">
        <rect x="720" y="590" width="80" height="15" rx="3" fill="white" stroke="#9C27B0" stroke-width="1"/>
        <rect x="720" y="610" width="80" height="15" rx="3" fill="white" stroke="#9C27B0" stroke-width="1"/>
        <rect x="720" y="630" width="80" height="15" rx="3" fill="white" stroke="#9C27B0" stroke-width="1"/>
        <text x="760" y="660" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
          初始种群
        </text>
        <text x="760" y="675" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
          (pop个解)
        </text>
      </g>
      
      <path d="M 630 620 L 700 620" stroke="white" stroke-width="2" marker-end="url(#whitearrow)"/>
    </g>
    
    <!-- 关键参数 -->
    <g id="key-parameters">
      <rect x="950" y="580" width="180" height="100" rx="5" fill="rgba(255,255,255,0.1)" stroke="white" stroke-width="1"/>
      <text x="1040" y="600" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">
        表示说明
      </text>
      <text x="960" y="620" font-family="Arial, sans-serif" font-size="10" fill="white">
        • 小圆圈 ○ = 单个节点
      </text>
      <text x="960" y="635" font-family="Arial, sans-serif" font-size="10" fill="white">
        • 矩形框 □ = 一个解(k个节点)
      </text>
      <text x="960" y="650" font-family="Arial, sans-serif" font-size="10" fill="white">
        • 绿色●/红色● = 扰动状态
      </text>
      <text x="960" y="665" font-family="Arial, sans-serif" font-size="10" fill="white">
        • k = 10~100 (种子集大小)
      </text>
    </g>
  </g>
  
  <!-- 输出箭头 -->
  <path d="M 600 760 L 600 780" stroke="#333" stroke-width="3" marker-end="url(#arrowhead)"/>
  <text x="600" y="795" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">
    输出：高质量初始种群 → 进入ANFDE主循环
  </text>
  
  <!-- 定义箭头标记 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
     refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <marker id="whitearrow" markerWidth="10" markerHeight="7" 
     refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="white" />
    </marker>
  </defs>
</svg>
