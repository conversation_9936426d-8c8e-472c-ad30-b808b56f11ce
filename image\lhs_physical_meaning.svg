<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义渐变色 -->
    <linearGradient id="regionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2196F3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#64B5F6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="vectorGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#81C784;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="samplingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF9800;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFB74D;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1400" height="900" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="700" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#333">
    LHS高维采样的物理意义解释 (k=10~100场景)
  </text>
  
  <!-- 第一部分：网络区域划分 -->
  <g id="network-regions">
    <rect x="50" y="70" width="600" height="200" rx="10" fill="url(#regionGradient)" stroke="#2196F3" stroke-width="2"/>
    <text x="350" y="95" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
      步骤1: 网络区域划分结果
    </text>
    
    <!-- 区域示意图 -->
    <g id="regions-visual">
      <!-- R0: 直径路径 -->
      <rect x="80" y="110" width="80" height="30" rx="5" fill="#D32F2F" stroke="white" stroke-width="1"/>
      <text x="120" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">R0: 5节点</text>
      
      <!-- R1: 距离=1 -->
      <rect x="180" y="110" width="80" height="30" rx="5" fill="#FF5722" stroke="white" stroke-width="1"/>
      <text x="220" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">R1: 8节点</text>
      
      <!-- R2: 距离=2 -->
      <rect x="280" y="110" width="80" height="30" rx="5" fill="#FF9800" stroke="white" stroke-width="1"/>
      <text x="320" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">R2: 12节点</text>
      
      <!-- R3: 距离=3 -->
      <rect x="380" y="110" width="80" height="30" rx="5" fill="#FFC107" stroke="white" stroke-width="1"/>
      <text x="420" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">R3: 15节点</text>
      
      <!-- R4: 距离=4 -->
      <rect x="480" y="110" width="80" height="30" rx="5" fill="#FFEB3B" stroke="white" stroke-width="1"/>
      <text x="520" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">R4: 20节点</text>
      
      <!-- 更多区域 -->
      <rect x="80" y="160" width="80" height="30" rx="5" fill="#8BC34A" stroke="white" stroke-width="1"/>
      <text x="120" y="180" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">R5: 18节点</text>
      
      <rect x="180" y="160" width="80" height="30" rx="5" fill="#4CAF50" stroke="white" stroke-width="1"/>
      <text x="220" y="180" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">R6: 22节点</text>
      
      <text x="320" y="180" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="white">...</text>
      
      <rect x="380" y="160" width="80" height="30" rx="5" fill="#009688" stroke="white" stroke-width="1"/>
      <text x="420" y="180" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">R15: 25节点</text>
      
      <rect x="480" y="160" width="80" height="30" rx="5" fill="#00BCD4" stroke="white" stroke-width="1"/>
      <text x="520" y="180" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">R16: 30节点</text>
    </g>
    
    <text x="350" y="220" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
      假设：网络划分出17个区域，种子集大小k=50
    </text>
    <text x="350" y="240" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
      effective_dims = min(50, 17) = 17 → 17维LHS采样
    </text>
  </g>
  
  <!-- 第二部分：LHS向量生成 -->
  <g id="lhs-vector">
    <rect x="700" y="70" width="650" height="200" rx="10" fill="url(#vectorGradient)" stroke="#4CAF50" stroke-width="2"/>
    <text x="1025" y="95" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
      步骤2: LHS向量生成 (17维)
    </text>
    
    <!-- LHS向量可视化 -->
    <g id="vector-visual">
      <text x="720" y="120" font-family="Arial, sans-serif" font-size="12" fill="white">
        lhs_sample = lhs(17, samples=1)[0] = 
      </text>
      
      <!-- 向量分量 -->
      <rect x="720" y="130" width="30" height="20" fill="#FFD54F" stroke="white" stroke-width="1"/>
      <text x="735" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">0.23</text>
      
      <rect x="760" y="130" width="30" height="20" fill="#FFD54F" stroke="white" stroke-width="1"/>
      <text x="775" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">0.67</text>
      
      <rect x="800" y="130" width="30" height="20" fill="#FFD54F" stroke="white" stroke-width="1"/>
      <text x="815" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">0.12</text>
      
      <rect x="840" y="130" width="30" height="20" fill="#FFD54F" stroke="white" stroke-width="1"/>
      <text x="855" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">0.89</text>
      
      <rect x="880" y="130" width="30" height="20" fill="#FFD54F" stroke="white" stroke-width="1"/>
      <text x="895" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">0.45</text>
      
      <text x="920" y="145" font-family="Arial, sans-serif" font-size="12" fill="white">...</text>
      
      <rect x="950" y="130" width="30" height="20" fill="#FFD54F" stroke="white" stroke-width="1"/>
      <text x="965" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">0.78</text>
      
      <rect x="990" y="130" width="30" height="20" fill="#FFD54F" stroke="white" stroke-width="1"/>
      <text x="1005" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">0.34</text>
      
      <!-- 维度标注 -->
      <text x="735" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">R0</text>
      <text x="775" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">R1</text>
      <text x="815" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">R2</text>
      <text x="855" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">R3</text>
      <text x="895" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">R4</text>
      <text x="965" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">R15</text>
      <text x="1005" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">R16</text>
    </g>
    
    <text x="1025" y="190" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
      17个[0,1]值，每个值对应一个区域的采样位置
    </text>
    
    <text x="1025" y="210" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
      这就是"17维超立方体"中的一个采样点
    </text>
    
    <text x="1025" y="230" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
      物理意义：在17个区域的联合空间中进行均匀采样
    </text>
  </g>
  
  <!-- 箭头 -->
  <path d="M 700 300 L 700 330" stroke="#333" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <!-- 第三部分：节点选择过程 -->
  <g id="node-selection">
    <rect x="50" y="350" width="1300" height="250" rx="10" fill="url(#samplingGradient)" stroke="#FF9800" stroke-width="2"/>
    <text x="700" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
      步骤3: 从LHS向量到节点选择的转换过程
    </text>
    
    <!-- 转换示例 -->
    <g id="conversion-examples">
      <!-- 示例1 -->
      <g id="example1">
        <rect x="80" y="390" width="200" height="80" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
        <text x="180" y="410" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
          R0区域 (5个节点)
        </text>
        <text x="90" y="430" font-family="Arial, sans-serif" font-size="11" fill="white">
          LHS值: 0.23
        </text>
        <text x="90" y="445" font-family="Arial, sans-serif" font-size="11" fill="white">
          索引: ⌊0.23 × 5⌋ = ⌊1.15⌋ = 1
        </text>
        <text x="90" y="460" font-family="Arial, sans-serif" font-size="11" fill="white">
          选择: R0[1] = 节点3
        </text>
      </g>
      
      <!-- 示例2 -->
      <g id="example2">
        <rect x="300" y="390" width="200" height="80" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
        <text x="400" y="410" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
          R1区域 (8个节点)
        </text>
        <text x="310" y="430" font-family="Arial, sans-serif" font-size="11" fill="white">
          LHS值: 0.67
        </text>
        <text x="310" y="445" font-family="Arial, sans-serif" font-size="11" fill="white">
          索引: ⌊0.67 × 8⌋ = ⌊5.36⌋ = 5
        </text>
        <text x="310" y="460" font-family="Arial, sans-serif" font-size="11" fill="white">
          选择: R1[5] = 节点12
        </text>
      </g>
      
      <!-- 示例3 -->
      <g id="example3">
        <rect x="520" y="390" width="200" height="80" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
        <text x="620" y="410" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
          R2区域 (12个节点)
        </text>
        <text x="530" y="430" font-family="Arial, sans-serif" font-size="11" fill="white">
          LHS值: 0.12
        </text>
        <text x="530" y="445" font-family="Arial, sans-serif" font-size="11" fill="white">
          索引: ⌊0.12 × 12⌋ = ⌊1.44⌋ = 1
        </text>
        <text x="530" y="460" font-family="Arial, sans-serif" font-size="11" fill="white">
          选择: R2[1] = 节点25
        </text>
      </g>
      
      <!-- 省略号 -->
      <text x="750" y="435" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" fill="white">...</text>
      
      <!-- 示例17 -->
      <g id="example17">
        <rect x="800" y="390" width="200" height="80" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
        <text x="900" y="410" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
          R16区域 (30个节点)
        </text>
        <text x="810" y="430" font-family="Arial, sans-serif" font-size="11" fill="white">
          LHS值: 0.34
        </text>
        <text x="810" y="445" font-family="Arial, sans-serif" font-size="11" fill="white">
          索引: ⌊0.34 × 30⌋ = ⌊10.2⌋ = 10
        </text>
        <text x="810" y="460" font-family="Arial, sans-serif" font-size="11" fill="white">
          选择: R16[10] = 节点87
        </text>
      </g>
    </g>
    
    <!-- 结果 -->
    <g id="result">
      <rect x="1050" y="390" width="250" height="80" rx="5" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="2"/>
      <text x="1175" y="410" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        LHS采样结果
      </text>
      <text x="1060" y="430" font-family="Arial, sans-serif" font-size="11" fill="white">
        部分解: {3, 12, 25, ..., 87}
      </text>
      <text x="1060" y="445" font-family="Arial, sans-serif" font-size="11" fill="white">
        节点数: 17个 (来自17个区域)
      </text>
      <text x="1060" y="460" font-family="Arial, sans-serif" font-size="11" fill="white">
        还需补充: 50-17=33个节点
      </text>
    </g>
    
    <!-- 关键洞察 -->
    <text x="700" y="500" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">
      关键洞察：每个LHS值控制在对应区域中的采样位置
    </text>
    <text x="700" y="520" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
      17维向量 → 17个区域同时采样 → 保证空间覆盖的均匀性
    </text>
    <text x="700" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
      这就是"高维LHS采样"的物理意义：多区域联合均匀采样
    </text>
    <text x="700" y="560" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
      当k=17时正好够用，当k>17时需要补充策略
    </text>
  </g>
  
  <!-- 箭头 -->
  <path d="M 700 620 L 700 650" stroke="#333" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <!-- 第四部分：补充策略 -->
  <g id="supplement-strategy">
    <rect x="50" y="670" width="1300" height="180" rx="10" fill="#9C27B0" stroke="#9C27B0" stroke-width="2"/>
    <text x="700" y="695" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
      步骤4: 三阶段补充策略 (17 → 50个节点)
    </text>
    
    <!-- 三个阶段 -->
    <g id="three-stages">
      <!-- 阶段1 -->
      <rect x="100" y="720" width="300" height="60" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
      <text x="250" y="740" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        阶段1: 区域轮询补充
      </text>
      <text x="110" y="760" font-family="Arial, sans-serif" font-size="11" fill="white">
        从每个区域选择综合评分最高的节点
      </text>
      <text x="110" y="775" font-family="Arial, sans-serif" font-size="11" fill="white">
        (跳过已选择的节点)
      </text>
      
      <!-- 阶段2 -->
      <rect x="450" y="720" width="300" height="60" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
      <text x="600" y="740" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        阶段2: 桥节点补充
      </text>
      <text x="460" y="760" font-family="Arial, sans-serif" font-size="11" fill="white">
        选择高于平均评分的桥节点
      </text>
      <text x="460" y="775" font-family="Arial, sans-serif" font-size="11" fill="white">
        按评分降序排列
      </text>
      
      <!-- 阶段3 -->
      <rect x="800" y="720" width="300" height="60" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
      <text x="950" y="740" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        阶段3: 外围节点补充
      </text>
      <text x="810" y="760" font-family="Arial, sans-serif" font-size="11" fill="white">
        选择不在任何区域中的节点
      </text>
      <text x="810" y="775" font-family="Arial, sans-serif" font-size="11" fill="white">
        按综合评分降序排列
      </text>
    </g>
    
    <text x="700" y="810" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
      最终结果：{3, 12, 25, ..., 87, 补充节点1, 补充节点2, ..., 补充节点33} = 50个节点
    </text>
    <text x="700" y="830" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
      保证了空间覆盖性(LHS) + 节点质量(综合评分) + 结构重要性(桥节点)
    </text>
  </g>
  
  <!-- 定义箭头标记 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
     refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
</svg>
