<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义渐变色 -->
    <linearGradient id="inputGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E3F2FD;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#BBDEFB;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="lambdaGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2196F3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#64B5F6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="stateGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF9800;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFB74D;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="paramGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9C27B0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#BA68C8;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1200" height="900" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#333">
    ANFDE主循环 - 准确流程 (第一部分: 景观分析→状态判断→参数更新)
  </text>
  
  <!-- 输入：初始种群 -->
  <g id="input-population">
    <rect x="50" y="60" width="1100" height="70" rx="10" fill="url(#inputGradient)" stroke="#1976D2" stroke-width="2"/>
    <text x="600" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#1976D2">
      输入：初始种群 population (来自混合初始化)
    </text>
    
    <!-- 种群个体表示 -->
    <g id="population-individuals">
      <rect x="150" y="100" width="80" height="20" rx="3" fill="rgba(33,150,243,0.3)" stroke="#1976D2" stroke-width="1"/>
      <circle cx="160" cy="110" r="2" fill="#1976D2"/>
      <circle cx="170" cy="110" r="2" fill="#1976D2"/>
      <circle cx="180" cy="110" r="2" fill="#1976D2"/>
      <circle cx="190" cy="110" r="2" fill="#1976D2"/>
      <circle cx="200" cy="110" r="2" fill="#1976D2"/>
      <circle cx="210" cy="110" r="2" fill="#1976D2"/>
      
      <rect x="300" y="100" width="80" height="20" rx="3" fill="rgba(33,150,243,0.3)" stroke="#1976D2" stroke-width="1"/>
      <circle cx="310" cy="110" r="2" fill="#1976D2"/>
      <circle cx="320" cy="110" r="2" fill="#1976D2"/>
      <circle cx="330" cy="110" r="2" fill="#1976D2"/>
      <circle cx="340" cy="110" r="2" fill="#1976D2"/>
      <circle cx="350" cy="110" r="2" fill="#1976D2"/>
      <circle cx="360" cy="110" r="2" fill="#1976D2"/>
      
      <text x="450" y="110" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#1976D2">...</text>
      
      <rect x="500" y="100" width="80" height="20" rx="3" fill="rgba(33,150,243,0.3)" stroke="#1976D2" stroke-width="1"/>
      <circle cx="510" cy="110" r="2" fill="#1976D2"/>
      <circle cx="520" cy="110" r="2" fill="#1976D2"/>
      <circle cx="530" cy="110" r="2" fill="#1976D2"/>
      <circle cx="540" cy="110" r="2" fill="#1976D2"/>
      <circle cx="550" cy="110" r="2" fill="#1976D2"/>
      <circle cx="560" cy="110" r="2" fill="#1976D2"/>
    </g>
  </g>
  
  <!-- 箭头 -->
  <path d="M 600 140 L 600 160" stroke="#333" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <!-- 步骤1：λ值计算 -->
  <g id="lambda-computation">
    <rect x="50" y="180" width="1100" height="140" rx="10" fill="url(#lambdaGradient)" stroke="#2196F3" stroke-width="2"/>
    <text x="600" y="205" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
      步骤1: 景观状态分析 - 计算λ值 (向量化高效实现)
    </text>
    
    <!-- λ计算流程 -->
    <g id="lambda-process">
      <!-- 距离矩阵计算 -->
      <rect x="80" y="220" width="200" height="80" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
      <text x="180" y="240" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        距离矩阵计算
      </text>
      <text x="90" y="260" font-family="Arial, sans-serif" font-size="10" fill="white">
        • 向量化PDI计算
      </text>
      <text x="90" y="275" font-family="Arial, sans-serif" font-size="10" fill="white">
        • PDI(S1,S2) = |S1△S2|/|S1∪S2|
      </text>
      <text x="90" y="290" font-family="Arial, sans-serif" font-size="10" fill="white">
        • 构建pop_size×pop_size矩阵
      </text>
      
      <!-- λ公式 -->
      <rect x="320" y="220" width="200" height="80" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
      <text x="420" y="240" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        λ值公式
      </text>
      <text x="330" y="260" font-family="Arial, sans-serif" font-size="11" fill="white">
        λ = (d_g - d_min) / (d_max - d_min)
      </text>
      <text x="330" y="275" font-family="Arial, sans-serif" font-size="10" fill="white">
        d_g: 最优个体平均距离
      </text>
      <text x="330" y="290" font-family="Arial, sans-serif" font-size="10" fill="white">
        d_max, d_min: 最大最小距离
      </text>
      
      <!-- 批量适应度计算 -->
      <rect x="560" y="220" width="200" height="80" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
      <text x="660" y="240" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        批量适应度计算
      </text>
      <text x="570" y="260" font-family="Arial, sans-serif" font-size="10" fill="white">
        • fitness_batch(population)
      </text>
      <text x="570" y="275" font-family="Arial, sans-serif" font-size="10" fill="white">
        • 找到最优个体索引
      </text>
      <text x="570" y="290" font-family="Arial, sans-serif" font-size="10" fill="white">
        • 计算平均距离向量
      </text>
      
      <!-- λ值结果 -->
      <rect x="800" y="220" width="200" height="80" rx="5" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="2"/>
      <text x="900" y="240" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        λ值输出
      </text>
      <text x="810" y="260" font-family="Arial, sans-serif" font-size="11" fill="white">
        λ ∈ [0, 1]
      </text>
      <text x="810" y="275" font-family="Arial, sans-serif" font-size="10" fill="white">
        反映种群多样性状态
      </text>
      <text x="810" y="290" font-family="Arial, sans-serif" font-size="10" fill="white">
        添加到lambda_history
      </text>
    </g>
  </g>
  
  <!-- 箭头 -->
  <path d="M 600 330 L 600 350" stroke="#333" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <!-- 步骤2：状态判断 -->
  <g id="state-determination">
    <rect x="50" y="370" width="1100" height="180" rx="10" fill="url(#stateGradient)" stroke="#FF9800" stroke-width="2"/>
    <text x="600" y="395" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
      步骤2: 自适应状态判断 - 动态阈值 + 逃逸检测
    </text>
    
    <!-- 状态判断流程 -->
    <g id="state-process">
      <!-- 前10代强制探索 -->
      <rect x="80" y="410" width="180" height="60" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
      <text x="170" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">
        前10代强制探索
      </text>
      <text x="90" y="450" font-family="Arial, sans-serif" font-size="10" fill="white">
        if self.gen &lt; 10:
      </text>
      <text x="100" y="465" font-family="Arial, sans-serif" font-size="10" fill="white">
        return 'exploration'
      </text>
      
      <!-- 逃逸条件检测 -->
      <rect x="280" y="410" width="180" height="60" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
      <text x="370" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">
        逃逸条件检测
      </text>
      <text x="290" y="450" font-family="Arial, sans-serif" font-size="10" fill="white">
        λ ≈ 0 且停滞计数 ≥ 阈值
      </text>
      <text x="290" y="465" font-family="Arial, sans-serif" font-size="10" fill="white">
        或适应度未提升
      </text>
      
      <!-- 动态阈值计算 -->
      <rect x="480" y="410" width="180" height="60" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
      <text x="570" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">
        动态阈值计算
      </text>
      <text x="490" y="450" font-family="Arial, sans-serif" font-size="10" fill="white">
        Q1 = percentile(λ_history, 25)
      </text>
      <text x="490" y="465" font-family="Arial, sans-serif" font-size="10" fill="white">
        Q3 = percentile(λ_history, 75)
      </text>
      
      <!-- 状态区间划分 -->
      <rect x="680" y="410" width="180" height="60" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
      <text x="770" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">
        状态区间划分
      </text>
      <text x="690" y="450" font-family="Arial, sans-serif" font-size="9" fill="white">
        收敛[0,Q1] 开发[Q1,(Q1+Q3)/2]
      </text>
      <text x="690" y="465" font-family="Arial, sans-serif" font-size="9" fill="white">
        探索[(Q1+Q3)/2,Q3] 逃逸[Q3,1]
      </text>
      
      <!-- 状态输出 -->
      <rect x="880" y="410" width="180" height="60" rx="5" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="2"/>
      <text x="970" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">
        当前状态输出
      </text>
      <text x="890" y="450" font-family="Arial, sans-serif" font-size="10" fill="white">
        current_state ∈ {convergence,
      </text>
      <text x="890" y="465" font-family="Arial, sans-serif" font-size="10" fill="white">
        exploitation, exploration, escape}
      </text>
    </g>
    
    <!-- 四种状态示意 -->
    <g id="four-states-demo">
      <rect x="100" y="490" width="120" height="40" rx="5" fill="#F18F01" stroke="white" stroke-width="1"/>
      <text x="160" y="515" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">收敛状态</text>
      
      <rect x="250" y="490" width="120" height="40" rx="5" fill="#C73E1D" stroke="white" stroke-width="1"/>
      <text x="310" y="515" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">开发状态</text>
      
      <rect x="400" y="490" width="120" height="40" rx="5" fill="#2E86AB" stroke="white" stroke-width="1"/>
      <text x="460" y="515" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">探索状态</text>
      
      <rect x="550" y="490" width="120" height="40" rx="5" fill="#A23B72" stroke="white" stroke-width="1"/>
      <text x="610" y="515" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">逃逸状态</text>
    </g>
  </g>
  
  <!-- 箭头 -->
  <path d="M 600 560 L 600 580" stroke="#333" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <!-- 步骤3：参数更新 -->
  <g id="parameter-update">
    <rect x="50" y="600" width="1100" height="120" rx="10" fill="url(#paramGradient)" stroke="#9C27B0" stroke-width="2"/>
    <text x="600" y="625" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
      步骤3: 动态参数更新 - 自适应CR和F值
    </text>
    
    <!-- 参数更新流程 -->
    <g id="param-process">
      <!-- 成功参数记录 -->
      <rect x="100" y="650" width="200" height="50" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
      <text x="200" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">
        成功参数记录
      </text>
      <text x="110" y="690" font-family="Arial, sans-serif" font-size="10" fill="white">
        success_CR[], success_F[]
      </text>
      
      <!-- 参数更新公式 -->
      <rect x="350" y="650" width="250" height="50" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
      <text x="475" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">
        参数更新公式
      </text>
      <text x="360" y="690" font-family="Arial, sans-serif" font-size="10" fill="white">
        μ_CR = (1-c)×μ_CR + c×mean(success_CR)
      </text>
      
      <!-- 参数生成 -->
      <rect x="650" y="650" width="200" height="50" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
      <text x="750" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">
        参数生成
      </text>
      <text x="660" y="690" font-family="Arial, sans-serif" font-size="10" fill="white">
        CR~N(μ_CR,0.1²), F~Cauchy(μ_F,0.1)
      </text>
      
      <!-- 参数输出 -->
      <rect x="900" y="650" width="150" height="50" rx="5" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="2"/>
      <text x="975" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">
        更新后参数
      </text>
      <text x="910" y="690" font-family="Arial, sans-serif" font-size="10" fill="white">
        新的μ_CR, μ_F
      </text>
    </g>
  </g>
  
  <!-- 输出箭头 -->
  <path d="M 600 730 L 600 750" stroke="#333" stroke-width="3" marker-end="url(#arrowhead)"/>
  <text x="600" y="770" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#333">
    继续 → 状态感知变异操作 (第二部分)
  </text>
  
  <!-- 关键信息框 -->
  <g id="key-info">
    <rect x="50" y="790" width="1100" height="80" rx="10" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2"/>
    <text x="600" y="815" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2E7D32">
      关键算法特点
    </text>
    <text x="70" y="840" font-family="Arial, sans-serif" font-size="12" fill="#2E7D32">
      • 向量化距离计算提高效率  • 动态阈值自适应状态划分  • 逃逸条件双重检测机制
    </text>
    <text x="70" y="860" font-family="Arial, sans-serif" font-size="12" fill="#2E7D32">
      • 成功参数自学习更新  • 前10代强制探索策略  • 停滞计数器防止局部最优
    </text>
  </g>
  
  <!-- 定义箭头标记 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
     refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
</svg>
