<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义渐变色 -->
    <linearGradient id="networkGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1976D2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#42A5F5;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="lhsGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#81C784;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="degreeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF9800;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFB74D;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="hybridGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9C27B0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#BA68C8;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cubeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF5722;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF8A65;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 背景 -->
  <rect width="1200" height="800" fill="#f8f9fa"/>

  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#333">
    初始化框架 - 高维LHS采样 (k=10~100)
  </text>

  <!-- 第一层：网络预处理 -->
  <g id="network-preprocessing">
    <rect x="50" y="60" width="1100" height="80" rx="10" fill="url(#networkGradient)" stroke="#1976D2" stroke-width="2"/>
    <text x="600" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
      网络预处理：直径路径划分 + 桥节点检测 + 综合中心性计算
    </text>

    <!-- 网络节点示意 -->
    <g id="network-nodes">
      <circle cx="100" cy="110" r="4" fill="white"/>
      <circle cx="120" cy="105" r="4" fill="white"/>
      <circle cx="140" cy="115" r="4" fill="white"/>
      <circle cx="160" cy="110" r="4" fill="white"/>
      <circle cx="180" cy="105" r="4" fill="white"/>
      <text x="140" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">网络图G</text>
    </g>

    <!-- 区域划分结果 -->
    <g id="region-result">
      <rect x="250" y="95" width="60" height="20" rx="3" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="1"/>
      <text x="280" y="108" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">R0</text>
      <rect x="320" y="95" width="60" height="20" rx="3" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="1"/>
      <text x="350" y="108" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">R1</text>
      <rect x="390" y="95" width="60" height="20" rx="3" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="1"/>
      <text x="420" y="108" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">R2</text>
      <text x="460" y="108" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">...</text>
      <rect x="480" y="95" width="60" height="20" rx="3" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="1"/>
      <text x="510" y="108" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">Rn</text>
    </g>

    <!-- 桥节点 -->
    <g id="bridge-nodes">
      <circle cx="600" cy="105" r="6" fill="#FFD54F" stroke="white" stroke-width="2"/>
      <circle cx="620" cy="110" r="6" fill="#FFD54F" stroke="white" stroke-width="2"/>
      <circle cx="640" cy="105" r="6" fill="#FFD54F" stroke="white" stroke-width="2"/>
      <text x="620" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">桥节点</text>
    </g>

    <!-- 综合评分 -->
    <g id="combined-scores">
      <rect x="750" y="95" width="150" height="20" rx="3" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="1"/>
      <text x="825" y="108" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">综合中心性评分</text>
    </g>
  </g>

  <!-- 箭头 -->
  <path d="M 600 150 L 600 170" stroke="#333" stroke-width="3" marker-end="url(#arrowhead)"/>

  <!-- 第二层：双路径采样 -->
  <g id="dual-sampling">
    <!-- LHS采样路径 -->
    <g id="lhs-path">
      <rect x="50" y="190" width="500" height="200" rx="10" fill="url(#lhsGradient)" stroke="#4CAF50" stroke-width="2"/>
      <text x="300" y="215" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
        路径1: 高维LHS采样 (effective_dims ≤ k)
      </text>

      <!-- 高维LHS采样表示 -->
      <g id="high-dim-lhs">
        <!-- LHS采样矩阵概念图 -->
        <rect x="120" y="240" width="120" height="80" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="2"/>
        <text x="180" y="235" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
          LHS采样向量
        </text>

        <!-- 维度轴表示 -->
        <text x="100" y="255" font-family="Arial, sans-serif" font-size="9" fill="white">R0</text>
        <text x="100" y="270" font-family="Arial, sans-serif" font-size="9" fill="white">R1</text>
        <text x="100" y="285" font-family="Arial, sans-serif" font-size="9" fill="white">R2</text>
        <text x="100" y="300" font-family="Arial, sans-serif" font-size="9" fill="white">...</text>
        <text x="100" y="315" font-family="Arial, sans-serif" font-size="9" fill="white">Rn</text>

        <!-- 采样值示例 -->
        <rect x="130" y="250" width="20" height="8" fill="#FFD54F"/>
        <text x="155" y="257" font-family="Arial, sans-serif" font-size="8" fill="white">0.3</text>

        <rect x="130" y="265" width="35" height="8" fill="#FFD54F"/>
        <text x="170" y="272" font-family="Arial, sans-serif" font-size="8" fill="white">0.7</text>

        <rect x="130" y="280" width="15" height="8" fill="#FFD54F"/>
        <text x="150" y="287" font-family="Arial, sans-serif" font-size="8" fill="white">0.1</text>

        <rect x="130" y="295" width="25" height="8" fill="#FFD54F"/>
        <text x="160" y="302" font-family="Arial, sans-serif" font-size="8" fill="white">0.5</text>

        <rect x="130" y="310" width="30" height="8" fill="#FFD54F"/>
        <text x="165" y="317" font-family="Arial, sans-serif" font-size="8" fill="white">0.6</text>

        <text x="180" y="335" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
          每个维度一个[0,1]值
        </text>
        <text x="180" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
          对应一个区域的采样
        </text>
      </g>

      <!-- LHS物理意义解释 -->
      <g id="lhs-meaning">
        <rect x="280" y="240" width="200" height="120" fill="rgba(255,255,255,0.1)" stroke="white" stroke-width="1"/>
        <text x="380" y="255" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
          LHS物理意义
        </text>

        <text x="290" y="275" font-family="Arial, sans-serif" font-size="11" fill="white">
          1. 向量 [0.3, 0.7, 0.1, 0.5, 0.6]
        </text>
        <text x="290" y="290" font-family="Arial, sans-serif" font-size="11" fill="white">
          2. 从R0选第⌊0.3×|R0|⌋个节点
        </text>
        <text x="290" y="305" font-family="Arial, sans-serif" font-size="11" fill="white">
          3. 从R1选第⌊0.7×|R1|⌋个节点
        </text>
        <text x="290" y="320" font-family="Arial, sans-serif" font-size="11" fill="white">
          4. 从R2选第⌊0.1×|R2|⌋个节点
        </text>
        <text x="290" y="335" font-family="Arial, sans-serif" font-size="11" fill="white">
          5. ... 直到effective_dims个区域
        </text>
        <text x="290" y="350" font-family="Arial, sans-serif" font-size="10" fill="white">
          → 得到部分解，再补充到k个节点
        </text>
      </g>
    </g>

    <!-- 度中心性采样路径 -->
    <g id="degree-path">
      <rect x="600" y="190" width="500" height="200" rx="10" fill="url(#degreeGradient)" stroke="#FF9800" stroke-width="2"/>
      <text x="850" y="215" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
        路径2: 度中心性采样 + 扰动
      </text>

      <!-- 度排序示意 -->
      <g id="degree-ranking">
        <circle cx="650" cy="250" r="12" fill="white" stroke="#FF9800" stroke-width="2"/>
        <text x="650" y="255" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#FF9800">高</text>

        <circle cx="680" cy="260" r="10" fill="white" stroke="#FF9800" stroke-width="2"/>
        <text x="680" y="265" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#FF9800">中</text>

        <circle cx="710" cy="270" r="8" fill="white" stroke="#FF9800" stroke-width="2"/>
        <text x="710" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#FF9800">低</text>

        <text x="680" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
          度值排序
        </text>
      </g>

      <!-- 扰动过程 -->
      <g id="perturbation">
        <text x="820" y="250" font-family="Arial, sans-serif" font-size="12" fill="white">
          for i in range(SN//2):
        </text>
        <text x="830" y="270" font-family="Arial, sans-serif" font-size="12" fill="white">
          solution = top_k_nodes(k)
        </text>
        <text x="830" y="290" font-family="Arial, sans-serif" font-size="12" fill="white">
          for j in range(k):
        </text>
        <text x="840" y="310" font-family="Arial, sans-serif" font-size="12" fill="white">
          if random() > 0.5:
        </text>
        <text x="850" y="330" font-family="Arial, sans-serif" font-size="12" fill="white">
          扰动替换节点
        </text>
        <text x="830" y="350" font-family="Arial, sans-serif" font-size="12" fill="white">
          保证质量+多样性
        </text>
      </g>
    </g>
  </g>

  <!-- 箭头 -->
  <path d="M 300 400 L 300 430" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 850 400 L 850 430" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 575 430 L 625 430" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>

  <!-- 第三层：混合初始化 -->
  <g id="hybrid-initialization">
    <rect x="50" y="450" width="1100" height="300" rx="10" fill="url(#hybridGradient)" stroke="#9C27B0" stroke-width="2"/>
    <text x="600" y="475" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
      混合初始化种群构建 - 分层筛选机制
    </text>

    <!-- 五个阶段 -->
    <g id="five-stages">
      <!-- 阶段0 -->
      <rect x="80" y="500" width="180" height="60" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
      <text x="170" y="520" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        阶段0: 适应度预计算
      </text>
      <text x="170" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
        并行缓存所有候选解
      </text>
      <text x="170" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
        的LIE_two_hop值
      </text>

      <!-- 阶段1 -->
      <rect x="280" y="500" width="180" height="60" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
      <text x="370" y="520" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        阶段1: 质量筛选
      </text>
      <text x="370" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
        保留适应度最高的50%
      </text>
      <text x="370" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
        LHS解 + 评分解
      </text>

      <!-- 阶段2 -->
      <rect x="480" y="500" width="180" height="60" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
      <text x="570" y="520" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        阶段2: 多样性筛选
      </text>
      <text x="570" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
        相似度阈值0.8
      </text>
      <text x="570" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
        去除冗余解
      </text>

      <!-- 阶段3 -->
      <rect x="680" y="500" width="180" height="60" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
      <text x="770" y="520" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        阶段3: 智能补充
      </text>
      <text x="770" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
        区域轮询→桥节点
      </text>
      <text x="770" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
        →外围节点
      </text>

      <!-- 阶段4 -->
      <rect x="880" y="500" width="180" height="60" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
      <text x="970" y="520" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        阶段4: 最终处理
      </text>
      <text x="970" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
        节点整数化+去重
      </text>
      <text x="970" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
        调整到目标大小
      </text>
    </g>

    <!-- 筛选流程示意 -->
    <g id="filtering-flow">
      <!-- LHS解集 -->
      <g id="lhs-solutions">
        <circle cx="150" cy="600" r="6" fill="#4CAF50"/>
        <circle cx="170" cy="590" r="6" fill="#4CAF50"/>
        <circle cx="190" cy="610" r="6" fill="#4CAF50"/>
        <circle cx="210" cy="595" r="6" fill="#4CAF50"/>
        <circle cx="230" cy="605" r="6" fill="#4CAF50"/>
        <text x="190" y="630" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
          LHS解集 (SN/2个)
        </text>
        <text x="190" y="645" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
          高维空间覆盖
        </text>
      </g>

      <!-- 评分解集 -->
      <g id="score-solutions">
        <circle cx="350" cy="600" r="6" fill="#FF9800"/>
        <circle cx="370" cy="590" r="6" fill="#FF9800"/>
        <circle cx="390" cy="610" r="6" fill="#FF9800"/>
        <circle cx="410" cy="595" r="6" fill="#FF9800"/>
        <circle cx="430" cy="605" r="6" fill="#FF9800"/>
        <text x="390" y="630" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
          评分解集 (SN/2个)
        </text>
        <text x="390" y="645" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
          质量保证+扰动
        </text>
      </g>

      <!-- 筛选箭头 -->
      <path d="M 250 600 L 300 600" stroke="white" stroke-width="2" marker-end="url(#whitearrow)"/>
      <path d="M 450 600 L 500 600" stroke="white" stroke-width="2" marker-end="url(#whitearrow)"/>

      <!-- 筛选过程 -->
      <rect x="520" y="580" width="120" height="40" rx="5" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="1"/>
      <text x="580" y="605" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
        质量+多样性筛选
      </text>

      <!-- 最终种群 -->
      <g id="final-population">
        <circle cx="750" cy="590" r="6" fill="white"/>
        <circle cx="770" cy="600" r="6" fill="white"/>
        <circle cx="790" cy="590" r="6" fill="white"/>
        <circle cx="810" cy="600" r="6" fill="white"/>
        <circle cx="830" cy="590" r="6" fill="white"/>
        <circle cx="850" cy="600" r="6" fill="white"/>
        <circle cx="870" cy="590" r="6" fill="white"/>
        <circle cx="890" cy="600" r="6" fill="white"/>
        <text x="820" y="630" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
          初始种群 (pop个)
        </text>
        <text x="820" y="645" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
          高质量+高多样性
        </text>
      </g>

      <path d="M 660 600 L 720 600" stroke="white" stroke-width="2" marker-end="url(#whitearrow)"/>
    </g>

    <!-- 关键参数 -->
    <g id="key-parameters">
      <rect x="950" y="580" width="180" height="80" rx="5" fill="rgba(255,255,255,0.1)" stroke="white" stroke-width="1"/>
      <text x="1040" y="600" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">
        关键参数
      </text>
      <text x="960" y="620" font-family="Arial, sans-serif" font-size="10" fill="white">
        • k = 10~100 (种子集大小)
      </text>
      <text x="960" y="635" font-family="Arial, sans-serif" font-size="10" fill="white">
        • SN = 2×pop (候选解数)
      </text>
      <text x="960" y="650" font-family="Arial, sans-serif" font-size="10" fill="white">
        • quality_proportion = 0.5
      </text>
    </g>
  </g>

  <!-- 输出箭头 -->
  <path d="M 600 760 L 600 780" stroke="#333" stroke-width="3" marker-end="url(#arrowhead)"/>
  <text x="600" y="795" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">
    输出：高质量初始种群 → 进入ANFDE主循环
  </text>

  <!-- 定义箭头标记 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7"
     refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <marker id="whitearrow" markerWidth="10" markerHeight="7"
     refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="white" />
    </marker>
  </defs>
</svg>
