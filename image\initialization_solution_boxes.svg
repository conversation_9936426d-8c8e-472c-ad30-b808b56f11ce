<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义渐变色 -->
    <linearGradient id="networkGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1976D2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#42A5F5;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="lhsGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#81C784;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="degreeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF9800;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFB74D;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="hybridGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9C27B0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#BA68C8;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cubeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF5722;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF8A65;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1200" height="800" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#333">
    初始化框架 - 解用矩形框+小圆圈表示 (k=10~100)
  </text>
  
  <!-- 第一层：网络预处理 -->
  <g id="network-preprocessing">
    <rect x="50" y="60" width="1100" height="80" rx="10" fill="url(#networkGradient)" stroke="#1976D2" stroke-width="2"/>
    <text x="600" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
      网络预处理：直径路径划分 + 桥节点检测 + 综合中心性计算
    </text>
    
    <!-- 网络节点示意 -->
    <g id="network-nodes">
      <circle cx="100" cy="110" r="3" fill="white"/>
      <circle cx="115" cy="105" r="3" fill="white"/>
      <circle cx="130" cy="115" r="3" fill="white"/>
      <circle cx="145" cy="110" r="3" fill="white"/>
      <circle cx="160" cy="105" r="3" fill="white"/>
      <text x="130" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">网络节点</text>
    </g>
    
    <!-- 区域划分结果 -->
    <g id="region-result">
      <rect x="250" y="95" width="60" height="20" rx="3" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="1"/>
      <text x="280" y="108" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">R0</text>
      <rect x="320" y="95" width="60" height="20" rx="3" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="1"/>
      <text x="350" y="108" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">R1</text>
      <rect x="390" y="95" width="60" height="20" rx="3" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="1"/>
      <text x="420" y="108" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">R2</text>
      <text x="460" y="108" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">...</text>
    </g>
  </g>
  
  <!-- 箭头 -->
  <path d="M 600 150 L 600 170" stroke="#333" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <!-- 第二层：双路径采样 -->
  <g id="dual-sampling">
    <!-- LHS采样路径 -->
    <g id="lhs-path">
      <rect x="50" y="190" width="500" height="220" rx="10" fill="url(#lhsGradient)" stroke="#4CAF50" stroke-width="2"/>
      <text x="300" y="215" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
        路径1: 高维LHS采样
      </text>
      
      <!-- 高维立方体表示 -->
      <g id="high-dim-cube">
        <rect x="80" y="240" width="50" height="50" fill="url(#cubeGradient)" stroke="#D32F2F" stroke-width="2" opacity="1"/>
        <rect x="90" y="230" width="50" height="50" fill="url(#cubeGradient)" stroke="#D32F2F" stroke-width="2" opacity="0.7"/>
        <rect x="100" y="220" width="50" height="50" fill="url(#cubeGradient)" stroke="#D32F2F" stroke-width="2" opacity="0.4"/>
        
        <circle cx="105" cy="265" r="2" fill="white"/>
        <circle cx="115" cy="255" r="2" fill="white"/>
        <circle cx="125" cy="245" r="2" fill="white"/>
        
        <text x="115" y="310" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
          高维采样空间
        </text>
      </g>
      
      <!-- LHS解表示 - 矩形框包含小圆圈 -->
      <g id="lhs-solutions-boxes">
        <text x="280" y="240" font-family="Arial, sans-serif" font-size="12" fill="white">
          LHS解集 (k=5):
        </text>
        
        <!-- 解1 -->
        <rect x="200" y="250" width="120" height="30" rx="5" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="2"/>
        <circle cx="210" cy="265" r="4" fill="#4CAF50"/>
        <circle cx="230" cy="265" r="4" fill="#4CAF50"/>
        <circle cx="250" cy="265" r="4" fill="#4CAF50"/>
        <circle cx="270" cy="265" r="4" fill="#4CAF50"/>
        <circle cx="290" cy="265" r="4" fill="#4CAF50"/>
        <text x="250" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">解1: 空间覆盖均匀</text>
        
        <!-- 解2 -->
        <rect x="200" y="305" width="120" height="30" rx="5" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="2"/>
        <circle cx="210" cy="320" r="4" fill="#4CAF50"/>
        <circle cx="230" cy="320" r="4" fill="#4CAF50"/>
        <circle cx="250" cy="320" r="4" fill="#4CAF50"/>
        <circle cx="270" cy="320" r="4" fill="#4CAF50"/>
        <circle cx="290" cy="320" r="4" fill="#4CAF50"/>
        <text x="250" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">解2: 来自不同区域</text>
        
        <!-- 解3 -->
        <rect x="200" y="360" width="120" height="30" rx="5" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="2"/>
        <circle cx="210" cy="375" r="4" fill="#4CAF50"/>
        <circle cx="230" cy="375" r="4" fill="#4CAF50"/>
        <circle cx="250" cy="375" r="4" fill="#4CAF50"/>
        <circle cx="270" cy="375" r="4" fill="#4CAF50"/>
        <circle cx="290" cy="375" r="4" fill="#4CAF50"/>
        <text x="250" y="405" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">解3: 多样性保证</text>
      </g>
    </g>
    
    <!-- 度中心性采样路径 -->
    <g id="degree-path">
      <rect x="600" y="190" width="500" height="220" rx="10" fill="url(#degreeGradient)" stroke="#FF9800" stroke-width="2"/>
      <text x="850" y="215" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
        路径2: 度中心性+扰动
      </text>
      
      <!-- 度排序示意 -->
      <g id="degree-ranking">
        <text x="630" y="240" font-family="Arial, sans-serif" font-size="11" fill="white">节点度值排序:</text>
        
        <circle cx="650" cy="255" r="8" fill="white" stroke="#FF9800" stroke-width="2"/>
        <text x="650" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#FF9800">1</text>
        
        <circle cx="680" cy="255" r="6" fill="white" stroke="#FF9800" stroke-width="2"/>
        <text x="680" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="7" fill="#FF9800">2</text>
        
        <circle cx="710" cy="255" r="4" fill="white" stroke="#FF9800" stroke-width="2"/>
        <text x="710" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="6" fill="#FF9800">3</text>
        
        <circle cx="740" cy="255" r="3" fill="white" stroke="#FF9800" stroke-width="2"/>
        <text x="740" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="6" fill="#FF9800">4</text>
        
        <circle cx="770" cy="255" r="2" fill="white" stroke="#FF9800" stroke-width="2"/>
        <text x="770" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="5" fill="#FF9800">5</text>
      </g>
      
      <!-- 度+扰动解表示 - 矩形框包含小圆圈，用颜色区分扰动 -->
      <g id="degree-solutions-boxes">
        <text x="780" y="290" font-family="Arial, sans-serif" font-size="12" fill="white">
          度+扰动解集 (k=5):
        </text>
        
        <!-- 解1 - 部分扰动 -->
        <rect x="650" y="300" width="120" height="30" rx="5" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="2"/>
        <circle cx="660" cy="315" r="4" fill="#4CAF50"/>  <!-- 保持 -->
        <circle cx="680" cy="315" r="4" fill="#F44336"/>  <!-- 扰动 -->
        <circle cx="700" cy="315" r="4" fill="#4CAF50"/>  <!-- 保持 -->
        <circle cx="720" cy="315" r="4" fill="#F44336"/>  <!-- 扰动 -->
        <circle cx="740" cy="315" r="4" fill="#4CAF50"/>  <!-- 保持 -->
        <text x="700" y="345" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">解1: 50%扰动</text>
        
        <!-- 解2 - 部分扰动 -->
        <rect x="650" y="355" width="120" height="30" rx="5" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="2"/>
        <circle cx="660" cy="370" r="4" fill="#F44336"/>  <!-- 扰动 -->
        <circle cx="680" cy="370" r="4" fill="#4CAF50"/>  <!-- 保持 -->
        <circle cx="700" cy="370" r="4" fill="#F44336"/>  <!-- 扰动 -->
        <circle cx="720" cy="370" r="4" fill="#4CAF50"/>  <!-- 保持 -->
        <circle cx="740" cy="370" r="4" fill="#F44336"/>  <!-- 扰动 -->
        <text x="700" y="400" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">解2: 质量+多样性</text>
        
        <text x="850" y="390" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
          绿色●:保持高度节点  红色●:随机替换
        </text>
      </g>
    </g>
  </g>
  
  <!-- 箭头 -->
  <path d="M 300 420 L 300 450" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 850 420 L 850 450" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 575 450 L 625 450" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 第三层：混合初始化 -->
  <g id="hybrid-initialization">
    <rect x="50" y="470" width="1100" height="280" rx="10" fill="url(#hybridGradient)" stroke="#9C27B0" stroke-width="2"/>
    <text x="600" y="495" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
      混合初始化种群构建 - 分层筛选机制
    </text>
    
    <!-- 五个阶段 -->
    <g id="five-stages">
      <rect x="80" y="520" width="160" height="50" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
      <text x="160" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">
        阶段0: 适应度预计算
      </text>
      <text x="160" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">
        并行缓存LIE_two_hop值
      </text>
      
      <rect x="260" y="520" width="160" height="50" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
      <text x="340" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">
        阶段1: 质量筛选
      </text>
      <text x="340" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">
        保留适应度最高50%
      </text>
      
      <rect x="440" y="520" width="160" height="50" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
      <text x="520" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">
        阶段2: 多样性筛选
      </text>
      <text x="520" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">
        相似度阈值0.8
      </text>
      
      <rect x="620" y="520" width="160" height="50" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
      <text x="700" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">
        阶段3: 智能补充
      </text>
      <text x="700" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">
        区域轮询→桥节点
      </text>
      
      <rect x="800" y="520" width="160" height="50" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
      <text x="880" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">
        阶段4: 最终处理
      </text>
      <text x="880" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">
        整数化+去重+调整
      </text>
    </g>
    
    <!-- 筛选流程示意 - 用矩形框包含小圆圈表示解集合 -->
    <g id="filtering-flow">
      <!-- LHS解集 -->
      <g id="lhs-solutions">
        <text x="150" y="600" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
          LHS解集 (SN/2个)
        </text>
        
        <rect x="100" y="610" width="100" height="25" rx="3" fill="rgba(76,175,80,0.3)" stroke="white" stroke-width="1"/>
        <circle cx="110" cy="622" r="3" fill="#4CAF50"/>
        <circle cx="125" cy="622" r="3" fill="#4CAF50"/>
        <circle cx="140" cy="622" r="3" fill="#4CAF50"/>
        <circle cx="155" cy="622" r="3" fill="#4CAF50"/>
        <circle cx="170" cy="622" r="3" fill="#4CAF50"/>
        <circle cx="185" cy="622" r="3" fill="#4CAF50"/>
        
        <rect x="100" y="640" width="100" height="25" rx="3" fill="rgba(76,175,80,0.3)" stroke="white" stroke-width="1"/>
        <circle cx="110" cy="652" r="3" fill="#4CAF50"/>
        <circle cx="125" cy="652" r="3" fill="#4CAF50"/>
        <circle cx="140" cy="652" r="3" fill="#4CAF50"/>
        <circle cx="155" cy="652" r="3" fill="#4CAF50"/>
        <circle cx="170" cy="652" r="3" fill="#4CAF50"/>
        <circle cx="185" cy="652" r="3" fill="#4CAF50"/>
        
        <text x="150" y="680" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">
          空间覆盖均匀
        </text>
      </g>
      
      <!-- 评分解集 -->
      <g id="score-solutions">
        <text x="350" y="600" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
          评分解集 (SN/2个)
        </text>
        
        <rect x="300" y="610" width="100" height="25" rx="3" fill="rgba(255,152,0,0.3)" stroke="white" stroke-width="1"/>
        <circle cx="310" cy="622" r="3" fill="#4CAF50"/>
        <circle cx="325" cy="622" r="3" fill="#F44336"/>
        <circle cx="340" cy="622" r="3" fill="#4CAF50"/>
        <circle cx="355" cy="622" r="3" fill="#F44336"/>
        <circle cx="370" cy="622" r="3" fill="#4CAF50"/>
        <circle cx="385" cy="622" r="3" fill="#F44336"/>
        
        <rect x="300" y="640" width="100" height="25" rx="3" fill="rgba(255,152,0,0.3)" stroke="white" stroke-width="1"/>
        <circle cx="310" cy="652" r="3" fill="#F44336"/>
        <circle cx="325" cy="652" r="3" fill="#4CAF50"/>
        <circle cx="340" cy="652" r="3" fill="#F44336"/>
        <circle cx="355" cy="652" r="3" fill="#4CAF50"/>
        <circle cx="370" cy="652" r="3" fill="#F44336"/>
        <circle cx="385" cy="652" r="3" fill="#4CAF50"/>
        
        <text x="350" y="680" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">
          质量+扰动多样性
        </text>
      </g>
      
      <!-- 筛选箭头 -->
      <path d="M 220 640 L 270 640" stroke="white" stroke-width="2" marker-end="url(#whitearrow)"/>
      <path d="M 420 640 L 470 640" stroke="white" stroke-width="2" marker-end="url(#whitearrow)"/>
      
      <!-- 筛选过程 -->
      <rect x="490" y="620" width="100" height="40" rx="5" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="1"/>
      <text x="540" y="645" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
        质量+多样性筛选
      </text>
      
      <!-- 最终种群 -->
      <g id="final-population">
        <text x="750" y="600" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
          初始种群 (pop个)
        </text>
        
        <rect x="700" y="610" width="100" height="25" rx="3" fill="rgba(255,255,255,0.4)" stroke="#9C27B0" stroke-width="2"/>
        <circle cx="710" cy="622" r="3" fill="white"/>
        <circle cx="725" cy="622" r="3" fill="white"/>
        <circle cx="740" cy="622" r="3" fill="white"/>
        <circle cx="755" cy="622" r="3" fill="white"/>
        <circle cx="770" cy="622" r="3" fill="white"/>
        <circle cx="785" cy="622" r="3" fill="white"/>
        
        <rect x="700" y="640" width="100" height="25" rx="3" fill="rgba(255,255,255,0.4)" stroke="#9C27B0" stroke-width="2"/>
        <circle cx="710" cy="652" r="3" fill="white"/>
        <circle cx="725" cy="652" r="3" fill="white"/>
        <circle cx="740" cy="652" r="3" fill="white"/>
        <circle cx="755" cy="652" r="3" fill="white"/>
        <circle cx="770" cy="652" r="3" fill="white"/>
        <circle cx="785" cy="652" r="3" fill="white"/>
        
        <text x="750" y="680" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">
          高质量+高多样性
        </text>
      </g>
      
      <path d="M 610 640 L 680 640" stroke="white" stroke-width="2" marker-end="url(#whitearrow)"/>
    </g>
    
    <!-- 表示说明 -->
    <g id="representation-guide">
      <rect x="950" y="580" width="180" height="120" rx="5" fill="rgba(255,255,255,0.1)" stroke="white" stroke-width="1"/>
      <text x="1040" y="600" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        表示说明
      </text>
      <text x="960" y="620" font-family="Arial, sans-serif" font-size="10" fill="white">
        • 矩形框 = 一个解(种子集)
      </text>
      <text x="960" y="635" font-family="Arial, sans-serif" font-size="10" fill="white">
        • 小圆圈 ○ = 解中的节点
      </text>
      <text x="960" y="650" font-family="Arial, sans-serif" font-size="10" fill="white">
        • 绿色● = 保持/LHS节点
      </text>
      <text x="960" y="665" font-family="Arial, sans-serif" font-size="10" fill="white">
        • 红色● = 扰动替换节点
      </text>
      <text x="960" y="680" font-family="Arial, sans-serif" font-size="10" fill="white">
        • 每个解包含k个节点
      </text>
    </g>
  </g>
  
  <!-- 输出箭头 -->
  <path d="M 600 760 L 600 780" stroke="#333" stroke-width="3" marker-end="url(#arrowhead)"/>
  <text x="600" y="795" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">
    输出：高质量初始种群 → 进入ANFDE主循环
  </text>
  
  <!-- 定义箭头标记 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
     refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <marker id="whitearrow" markerWidth="10" markerHeight="7" 
     refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="white" />
    </marker>
  </defs>
</svg>
