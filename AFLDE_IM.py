import time
from LIE import *  # 有LIE_two_hop 函数
from utils import *  # 有solution_similarity quality_filter diversity_filter函数
from anfde import *  # 有ANFDE类，主要是涉及后面的物种形成的种群和拥挤种群的CR和F参数的独立
from graph import *  # 有read_graph_from_file
from IC import *  # 有 mc_influence
from sample_solutions import *  # 有sample_lhs sample_score
import networkx as nx
import datetime
import os
import numpy as np
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import multiprocessing as mp

# ==================1.初始化函数 (initialize_population_hybrid) - ================

"""使用LHS解和评分解初始化种群，同时进行质量和多样性筛选"""
def initialize_population_hybrid(
        G,
        lhs_solutions,
        score_solutions,
        population_size,
        p,
        quality_proportion,
        similarity_threshold,
        bridge_nodes
):
    """"""
    """
    使用LHS解和评分解初始化种群，同时进行质量和多样性筛选
    :param G:
    :param lhs_solutions: 通过拉丁超立方采样生成的初始解集合。
    :param score_solutions: 度+扰动生成的采样解集合。
    :param population_size: 种群目标大小。
    :param p: 激活概率
    :param quality_proportion:  种群中高质量解的比例。
    :param similarity_threshold: 用于过滤相似解的阈值。
    :param bridge_nodes: 网络图中识别出的桥节点。
    :return:
    """

    print("\n" + "=" * 30 + " 初始化阶段开始 " + "=" * 30)

    # ================== 阶段0: 并行缓存所有候选解的适应度值 =================
    print("\n[阶段0] 并行预计算所有候选解的适应度值...")

    # 合并所有候选解
    all_solutions = lhs_solutions + score_solutions

    # 使用线程池而不是进程池来避免序列化问题
    fitness_cache = {}

    # 分批处理以优化性能
    batch_size = max(1, len(all_solutions) // 4)  # 使用固定的批次数
    batches = [all_solutions[i:i + batch_size] for i in range(0, len(all_solutions), batch_size)]

    def compute_fitness_batch(solutions_batch):
        return {tuple(sol): LIE_two_hop(sol, G, p) for sol in solutions_batch}

    # 使用ThreadPoolExecutor替代ProcessPoolExecutor
    with ThreadPoolExecutor(max_workers=4) as executor:
        batch_results = list(executor.map(compute_fitness_batch, batches))
        for batch_cache in batch_results:
            fitness_cache.update(batch_cache)

    print(f"适应度值缓存完成，共缓存{len(fitness_cache)}个解的适应度值.")

    # ================== 阶段1: 质量筛选 50% ==================
    print(f"\n[阶段1] 质量筛选 (质量比例={quality_proportion})")
    n_quality = int(population_size * quality_proportion)
    print(f"目标保留数: LHS={n_quality}, 评分={n_quality}")

    # LHS质量筛选：使用预计算的缓存
    print("\n[LHS] 原始解数量:", len(lhs_solutions))
    # 从 lhs_solutions中挑选适应度最高的解
    lhs_quality = quality_filter(lhs_solutions, fitness_cache, n_quality)
    print("[LHS] 筛选后保留:", len(lhs_quality))

    # 评分质量筛选：使用预计算的缓存
    print("\n[评分] 原始解数量:", len(score_solutions))
    # 从score_solutions中挑选适应度最高的解
    score_quality = quality_filter(score_solutions, fitness_cache, n_quality)
    print("[评分] 筛选后保留:", len(score_quality))

    # ================== 阶段2: 合并解集 ==================
    combined_solutions = lhs_quality + score_quality
    print(f"\n[阶段2] 合并后解总数: {len(combined_solutions)} (LHS+评分)")

    # ================== 阶段3: 多样性筛选 ==================
    # 确保种群的多样性，以避免过早收敛。
    # 根据 similarity_threshold 筛选合并后的解集，确保相似的解不会进入最终种群。
    print(f"\n[阶段3] 多样性筛选 (阈值={similarity_threshold})")
    initial_population = diversity_filter(combined_solutions, similarity_threshold)
    print(f"筛选后种群大小: {len(initial_population)} (目标: {population_size})")

    # ================== 阶段 4: 补充解 ==================
    print("\n[阶段4] 补充解过程开始")
    # 动态维护剩余候选解集合和已选解集合
    selected_solutions = set(tuple(sol) for sol in initial_population)# 已选解
    remaining_solutions = set(tuple(sol) for sol in lhs_solutions + score_solutions) - selected_solutions  # 剩余解

    iteration = 0
    while len(initial_population) < population_size:
        iteration += 1
        print(f"\n--- 补充迭代 {iteration} ---")
        print("当前种群大小:", len(initial_population))
        print("剩余候选解数量:", len(remaining_solutions))

        if not remaining_solutions:
            # 生成全新解
            print("\n[补充] 生成全新解")
            bridge_sample_size = min(2, len(bridge_nodes))
            non_bridge_size = population_size - bridge_sample_size

            bridge_sample = random.sample(bridge_nodes, bridge_sample_size)
            non_bridge_nodes = [n for n in G.nodes if n not in bridge_nodes]
            non_bridge_sample = random.sample(non_bridge_nodes, non_bridge_size)

            new_sol = bridge_sample + non_bridge_sample
            print(f"桥节点采样: {bridge_sample}")
            print(f"非桥节点采样: {non_bridge_sample}")
            print("生成新解:", new_sol)

            initial_population.append(new_sol)
        else:
            # 从剩余候选解中选择最优解 (利用缓存加速选择)
            print("\n[补充] 选择剩余最优解")
            best_remaining = max(remaining_solutions, key=lambda x: fitness_cache[x])
            fitness = fitness_cache[best_remaining]
            print("选择解:", best_remaining)
            print("适应度:", f"{fitness:.4f}")

            remaining_solutions.remove(best_remaining)
            selected_solutions.add(best_remaining)
            initial_population.append(list(best_remaining))

        print("补充后种群大小:", len(initial_population))

    # ================== 阶段5: 最终处理 ==================
    print("\n[阶段5] 最终处理")
    print("转换前最后一个解示例:", initial_population[-1])

    # 转换为整数节点
    initial_population = [[int(node) for node in individual] for individual in initial_population]
    print("转换后最后一个解示例:", initial_population[-1])

    # 去重检查
    unique_solutions = []
    seen = set()
    for sol in initial_population:
        sorted_sol = tuple(sorted(sol))
        if sorted_sol not in seen:
            seen.add(sorted_sol)
            unique_solutions.append(sol)
    print(f"去重后种群大小: {len(unique_solutions)}")

    print("\n" + "=" * 30 + " 初始化阶段完成 " + "=" * 30)
    return unique_solutions[:population_size]


# ========================主调用=============================
def ANFDE_IM(G, k, g, pop, FEsMaxs, p, SN, file=None):  # 添加 file 参数
    start_time = time.time()  # 记录函数开始时间
    bridge_nodes = detect_bridge_nodes(G) # 预计算桥节点
    combined_scores = calculate_combined_centrality_igraph(G) # 预计算节点综合评分
    # ========================1. 使用 LHS 和基于度采样生成初始解====================
    lhs_solutions = sample_lhs(G, k, SN // 2, bridge_nodes, combined_scores)
    score_solutions = sample_score(G, k, SN // 2)

    # 1.1 打印 LHS 解及其适应度值
    # print("\n=== LHS 解 ===")
    # for i, solution in enumerate(lhs_solutions):
    #     fitness = LIE_two_hop(solution, G, p)
    #     print(f"LHS解 {i + 1}: 节点集合: {sorted(solution)}, 适应度: {fitness:.4f}")
    # 1.2 打印基于评分的解及其适应度值
    # print("\n=== 基于评分的解 ===")
    # for i, solution in enumerate(score_solutions):
    #     fitness = LIE_two_hop(solution, G, p)
    #     print(f"评分解 {i + 1}: 节点集合: {sorted(solution)}, 适应度: {fitness:.4f}")

    # ------------------2. 初始化种群---------------------------
    initial_population = initialize_population_hybrid(G, lhs_solutions, score_solutions, pop, p,
                                                     quality_proportion=0.5, similarity_threshold=0.8,
                                                     bridge_nodes=bridge_nodes)
    # 5. 打印初始化种群的信息
    print("\n=== 初始化种群 ===")
    for i, solution in enumerate(initial_population):  # 遍历初始种群
        fitness = LIE_two_hop(solution, G, p)
        print(f"初始化种群解 {i + 1}: 节点集合: {sorted(solution)}, 适应度: {fitness:.4f}")

    # 计算初始景观状态值 λ
    # ------------------3. 实例化 ANFDE 类----------------------
    print(f"开始计算初始景观状态值λ：")
    anfde = ANFDE(G, p, bridge_nodes=bridge_nodes, k=k, distance_type='symmetric')  # 实例化 ANFDE 类
    initial_lambda = anfde._compute_lambda(initial_population)
    print(f"计算完成：")
    print(f"开始识别状态：")
    initial_state = anfde._determine_state(initial_lambda, initialization=True)
    print(f"状态识别完毕：")
    print(f"\n初始景观状态值 λ: {initial_lambda:.4f}, 初始状态: {initial_state}")
    # 初始化增强多样性
    if initial_lambda < 0.3:
        print(f"种群多样性不足，需增强：只是提示，先不增强")

        # ------------------4. 运行 ANFDE 算法----------------------
    print("\n" + "=" * 30 + " 开始运行 ANFDE 主循环 " + "=" * 30)
    final_population, fitness_history, lambda_history, state_history, best_individual, best_fitness, running_time \
        = anfde.run(
        initial_population=initial_population,
        k=k, g=g, FEsMaxs=FEsMaxs,
        lhs_solutions=lhs_solutions,  # <--- 传递 LHS 解
        score_solutions=score_solutions,  # <--- 传递评分解

        )
    print("\n" + "=" * 30 + " ANFDE 主循环结束 " + "=" * 30)

    # 5. 打印结果  (关键修改：处理输出格式)
    print("\n最终景观状态值 λ 历史:")
    print([float(x) for x in lambda_history])  # 将numpy.float64转换为float
    print("\n最终状态历史:")
    print(state_history)
    print("\n适应度历史:")
    print([float(x) for x in fitness_history])  # 将numpy.float64转换为float

    # 保存结果图表（包括矢量图）
    print("\n保存结果图表...")
    saved_files = anfde.plot_results(save_vector=True)
    print(f"已保存图表文件: {saved_files}")

    return final_population, fitness_history


def run_performance_test():
    """运行性能测试，比较优化前后的性能"""
    print("=" * 60)
    print("ANFDE-IM 算法性能测试")
    print("=" * 60)

    # 测试不同规模的网络
    test_networks = [
        ("networks/karate.txt", "Karate Club"),
        ("networks/blog-int.txt", "Blog Network"),
        # 可以添加更多网络进行测试
    ]

    for file_path, network_name in test_networks:
        print(f"\n{'='*20} 测试网络: {network_name} {'='*20}")

        try:
            # 加载网络
            G = gen_graph(file_path)
            isolates = list(nx.isolates(G))
            G.remove_nodes_from(isolates)

            print(f"网络规模: {G.number_of_nodes()} 节点, {G.number_of_edges()} 边")

            # 测试参数
            k = min(10, G.number_of_nodes() // 10)  # 自适应种子集大小
            p = 0.05
            pop = 20  # 较小的种群用于快速测试
            g = 50    # 较少的迭代次数
            FEsMaxs = 1000 * k
            SN = 100

            print(f"测试参数: k={k}, pop={pop}, g={g}, FEsMaxs={FEsMaxs}")

            # 运行算法
            start_time = time.time()
            final_population, final_fitness = ANFDE_IM(G, k, g, pop, FEsMaxs, p, SN, file=file_path)
            end_time = time.time()

            print(f"运行时间: {end_time - start_time:.2f} 秒")
            print(f"最终最佳适应度: {max(final_fitness):.4f}")

        except Exception as e:
            print(f"测试 {network_name} 时出错: {e}")
            continue

if __name__ == '__main__':
    # 选择运行模式
    mode = input("选择运行模式 (1: 标准运行, 2: 性能测试): ").strip()

    if mode == "2":
        run_performance_test()
    else:
        # ==================== 1. 数据加载与预处理 ================
        file = "networks/ws.txt"
        G = gen_graph(file)  # 假设该函数1.返回 networkx.Graph 对象

        # 移除孤立节点
        isolates = list(nx.isolates(G))
        G.remove_nodes_from(isolates)
        print(f"移除孤立节点数量: {len(isolates)}")
        print(f"剩余节点数: {G.number_of_nodes()}, 边数: {G.number_of_edges()}")

        # ==================== 2. 参数设置 ====================
        ks = [100]  # 种子集大小
        for k in ks:
            p = 0.05  # 激活概率
            pop = 30  # 种群大小
            g = 200  # 迭代次数
            FEsMaxs = 5000 * k  # 最大函数评估次数
            SN = 500

            print(f"\n开始运行 ANFDE-IM 算法 (优化版)")
            print(f"参数设置: k={k}, pop={pop}, g={g}, FEsMaxs={FEsMaxs}, SN={SN}")

            start_time = time.time()
            final_population, final_fitness = ANFDE_IM(G, k, g, pop, FEsMaxs, p, SN, file=file)
            end_time = time.time()

            print(f"\n算法运行完成!")
            print(f"总运行时间: {end_time - start_time:.2f} 秒")
            print(f"最终最佳适应度: {max(final_fitness):.4f}")
            print(f"使用CPU核心数: {mp.cpu_count()}")
            print("优化特性: 并行计算、向量化操作、智能缓存、矢量图输出")



