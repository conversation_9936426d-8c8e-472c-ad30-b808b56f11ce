<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义渐变色和图案 -->
    <radialGradient id="lambdaRadial" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#64B5F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1976D2;stop-opacity:1" />
    </radialGradient>
    
    <linearGradient id="stateSpectrum" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#F18F01;stop-opacity:1" />
      <stop offset="33%" style="stop-color:#C73E1D;stop-opacity:1" />
      <stop offset="66%" style="stop-color:#2E86AB;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#A23B72;stop-opacity:1" />
    </linearGradient>
    
    <pattern id="mutationPattern" patternUnits="userSpaceOnUse" width="20" height="20">
      <rect width="20" height="20" fill="#4CAF50"/>
      <circle cx="10" cy="10" r="3" fill="#81C784"/>
    </pattern>
    
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- DNA螺旋图案 -->
    <pattern id="dnaPattern" patternUnits="userSpaceOnUse" width="40" height="40">
      <rect width="40" height="40" fill="#9C27B0"/>
      <path d="M0,20 Q10,10 20,20 Q30,30 40,20" stroke="#BA68C8" stroke-width="2" fill="none"/>
      <path d="M0,20 Q10,30 20,20 Q30,10 40,20" stroke="#E1BEE7" stroke-width="2" fill="none"/>
    </pattern>
  </defs>
  
  <!-- 背景 -->
  <rect width="1400" height="1000" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="700" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="22" font-weight="bold" fill="#333">
    ANFDE主循环 - 创新可视化设计 (景观感知 → 智能进化)
  </text>
  
  <!-- 输入种群 - 用DNA链表示 -->
  <g id="input-population-dna">
    <rect x="50" y="60" width="1300" height="80" rx="15" fill="url(#dnaPattern)" stroke="#9C27B0" stroke-width="3"/>
    <text x="700" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="white">
      🧬 输入种群 - 遗传信息载体
    </text>
    
    <!-- DNA链式个体表示 -->
    <g id="dna-individuals">
      <!-- 个体1 -->
      <g id="individual1">
        <ellipse cx="200" cy="115" rx="40" ry="15" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="2"/>
        <circle cx="185" cy="115" r="4" fill="#FFD54F"/>
        <circle cx="195" cy="115" r="4" fill="#4CAF50"/>
        <circle cx="205" cy="115" r="4" fill="#2196F3"/>
        <circle cx="215" cy="115" r="4" fill="#FF5722"/>
        <text x="200" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">基因链1</text>
      </g>
      
      <!-- 个体2 -->
      <g id="individual2">
        <ellipse cx="400" cy="115" rx="40" ry="15" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="2"/>
        <circle cx="385" cy="115" r="4" fill="#9C27B0"/>
        <circle cx="395" cy="115" r="4" fill="#FF9800"/>
        <circle cx="405" cy="115" r="4" fill="#607D8B"/>
        <circle cx="415" cy="115" r="4" fill="#795548"/>
        <text x="400" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">基因链2</text>
      </g>
      
      <!-- 省略号 -->
      <text x="600" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" fill="white">⋯</text>
      
      <!-- 个体N -->
      <g id="individualN">
        <ellipse cx="800" cy="115" rx="40" ry="15" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="2"/>
        <circle cx="785" cy="115" r="4" fill="#E91E63"/>
        <circle cx="795" cy="115" r="4" fill="#00BCD4"/>
        <circle cx="805" cy="115" r="4" fill="#8BC34A"/>
        <circle cx="815" cy="115" r="4" fill="#FFC107"/>
        <text x="800" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">基因链N</text>
      </g>
    </g>
  </g>
  
  <!-- 流动箭头 -->
  <path d="M 700 150 Q 720 170 700 190" stroke="#333" stroke-width="4" fill="none" marker-end="url(#flowArrow)"/>
  
  <!-- 景观分析 - 用雷达图表示 -->
  <g id="landscape-radar">
    <rect x="50" y="210" width="1300" height="180" rx="15" fill="url(#lambdaRadial)" stroke="#2196F3" stroke-width="3"/>
    <text x="700" y="235" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="white">
      🎯 景观状态雷达 - λ值多维分析
    </text>
    
    <!-- 雷达图中心 -->
    <g id="radar-center">
      <circle cx="300" cy="320" r="80" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="3"/>
      <circle cx="300" cy="320" r="60" fill="rgba(255,255,255,0.1)" stroke="white" stroke-width="2"/>
      <circle cx="300" cy="320" r="40" fill="rgba(255,255,255,0.1)" stroke="white" stroke-width="2"/>
      <circle cx="300" cy="320" r="20" fill="rgba(255,255,255,0.1)" stroke="white" stroke-width="2"/>
      
      <!-- 雷达轴线 -->
      <line x1="300" y1="240" x2="300" y2="400" stroke="white" stroke-width="2"/>
      <line x1="220" y1="320" x2="380" y2="320" stroke="white" stroke-width="2"/>
      <line x1="243" y1="263" x2="357" y2="377" stroke="white" stroke-width="2"/>
      <line x1="357" y1="263" x2="243" y2="377" stroke="white" stroke-width="2"/>
      
      <!-- λ值指示器 -->
      <polygon points="300,280 320,300 300,340 280,300" fill="#FFD54F" stroke="white" stroke-width="2" filter="url(#glow)"/>
      <text x="300" y="325" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">λ=0.65</text>
      
      <!-- 维度标签 -->
      <text x="300" y="230" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">多样性</text>
      <text x="390" y="325" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">收敛性</text>
      <text x="300" y="415" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">分布性</text>
      <text x="210" y="325" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">聚集性</text>
    </g>
    
    <!-- 计算公式可视化 -->
    <g id="formula-visual">
      <rect x="450" y="260" width="300" height="120" rx="10" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="2"/>
      <text x="600" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">
        📊 λ计算矩阵
      </text>
      
      <!-- 距离矩阵可视化 -->
      <g id="distance-matrix">
        <rect x="470" y="300" width="20" height="20" fill="#FF5722" opacity="0.8"/>
        <rect x="495" y="300" width="20" height="20" fill="#FF9800" opacity="0.8"/>
        <rect x="520" y="300" width="20" height="20" fill="#FFC107" opacity="0.8"/>
        <rect x="545" y="300" width="20" height="20" fill="#4CAF50" opacity="0.8"/>
        
        <rect x="470" y="325" width="20" height="20" fill="#FF9800" opacity="0.8"/>
        <rect x="495" y="325" width="20" height="20" fill="#FF5722" opacity="0.8"/>
        <rect x="520" y="325" width="20" height="20" fill="#2196F3" opacity="0.8"/>
        <rect x="545" y="325" width="20" height="20" fill="#9C27B0" opacity="0.8"/>
        
        <text x="600" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">
          PDI距离热力图
        </text>
      </g>
    </g>
    
    <!-- 状态指示灯 -->
    <g id="state-indicators">
      <rect x="800" y="260" width="500" height="120" rx="10" fill="rgba(255,255,255,0.1)" stroke="white" stroke-width="2"/>
      <text x="1050" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">
        🚦 状态指示系统
      </text>
      
      <!-- 状态灯 -->
      <circle cx="850" cy="320" r="15" fill="#F18F01" filter="url(#glow)"/>
      <text x="850" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">收敛</text>
      
      <circle cx="950" cy="320" r="15" fill="#C73E1D"/>
      <text x="950" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">开发</text>
      
      <circle cx="1050" cy="320" r="15" fill="#2E86AB"/>
      <text x="1050" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">探索</text>
      
      <circle cx="1150" cy="320" r="15" fill="#A23B72"/>
      <text x="1150" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">逃逸</text>
    </g>
  </g>
  
  <!-- 流动箭头 -->
  <path d="M 700 400 Q 720 420 700 440" stroke="#333" stroke-width="4" fill="none" marker-end="url(#flowArrow)"/>
  
  <!-- 状态光谱 -->
  <g id="state-spectrum">
    <rect x="50" y="460" width="1300" height="120" rx="15" fill="url(#stateSpectrum)" stroke="#FF9800" stroke-width="3"/>
    <text x="700" y="485" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="white">
      🌈 进化状态光谱 - 自适应阈值划分
    </text>
    
    <!-- 光谱条 -->
    <rect x="200" y="510" width="800" height="30" fill="url(#stateSpectrum)" rx="15" stroke="white" stroke-width="2"/>
    
    <!-- 阈值标记 -->
    <line x1="350" y1="500" x2="350" y2="550" stroke="white" stroke-width="3"/>
    <text x="350" y="495" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">Q1</text>
    
    <line x1="600" y1="500" x2="600" y2="550" stroke="white" stroke-width="3"/>
    <text x="600" y="495" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">(Q1+Q3)/2</text>
    
    <line x1="850" y1="500" x2="850" y2="550" stroke="white" stroke-width="3"/>
    <text x="850" y="495" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">Q3</text>
    
    <!-- 当前位置指示器 -->
    <polygon points="650,500 660,510 650,520 640,510" fill="#FFD54F" stroke="white" stroke-width="2" filter="url(#glow)"/>
    <text x="650" y="565" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
      当前状态: 探索模式
    </text>
  </g>
  
  <!-- 流动箭头 -->
  <path d="M 700 590 Q 720 610 700 630" stroke="#333" stroke-width="4" fill="none" marker-end="url(#flowArrow)"/>
  
  <!-- 变异工厂 -->
  <g id="mutation-factory">
    <rect x="50" y="650" width="1300" height="200" rx="15" fill="url(#mutationPattern)" stroke="#4CAF50" stroke-width="3"/>
    <text x="700" y="675" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="white">
      🏭 智能变异工厂 - 状态感知基因重组
    </text>
    
    <!-- 变异流水线 -->
    <g id="mutation-pipeline">
      <!-- 输入传送带 -->
      <rect x="100" y="700" width="200" height="40" rx="20" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="2"/>
      <text x="200" y="725" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">原始基因</text>
      
      <!-- 基因在传送带上 -->
      <ellipse cx="150" cy="720" rx="25" ry="12" fill="rgba(255,255,255,0.5)" stroke="white" stroke-width="1"/>
      <circle cx="140" cy="720" r="3" fill="#FFD54F"/>
      <circle cx="150" cy="720" r="3" fill="#4CAF50"/>
      <circle cx="160" cy="720" r="3" fill="#2196F3"/>
      
      <!-- 变异机器 -->
      <rect x="350" y="690" width="120" height="80" rx="10" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="3"/>
      <text x="410" y="710" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">⚙️ 变异引擎</text>
      
      <!-- 齿轮动画效果 -->
      <circle cx="380" cy="730" r="15" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="2"/>
      <circle cx="440" cy="730" r="15" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="2"/>
      <polygon points="380,720 385,725 380,740 375,725" fill="white"/>
      <polygon points="440,720 445,725 440,740 435,725" fill="white"/>
      
      <text x="410" y="755" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">DE策略选择</text>
      
      <!-- 参数控制面板 -->
      <rect x="520" y="690" width="150" height="80" rx="10" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="2"/>
      <text x="595" y="710" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">🎛️ 参数面板</text>
      
      <!-- 控制旋钮 -->
      <circle cx="550" cy="735" r="12" fill="rgba(255,255,255,0.4)" stroke="white" stroke-width="2"/>
      <circle cx="595" cy="735" r="12" fill="rgba(255,255,255,0.4)" stroke="white" stroke-width="2"/>
      <circle cx="640" cy="735" r="12" fill="rgba(255,255,255,0.4)" stroke="white" stroke-width="2"/>
      
      <line x1="550" y1="730" x2="555" y2="740" stroke="white" stroke-width="2"/>
      <line x1="595" y1="725" x2="595" y2="735" stroke="white" stroke-width="2"/>
      <line x1="635" y1="735" x2="645" y2="735" stroke="white" stroke-width="2"/>
      
      <text x="550" y="755" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">CR</text>
      <text x="595" y="755" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">F</text>
      <text x="640" y="755" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">λ</text>
      
      <!-- 输出传送带 -->
      <rect x="720" y="700" width="200" height="40" rx="20" fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="2"/>
      <text x="820" y="725" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">变异基因</text>
      
      <!-- 变异后基因 -->
      <ellipse cx="870" cy="720" rx="25" ry="12" fill="rgba(255,255,255,0.5)" stroke="white" stroke-width="1"/>
      <circle cx="860" cy="720" r="3" fill="#FF5722"/>  <!-- 变异 -->
      <circle cx="870" cy="720" r="3" fill="#4CAF50"/>  <!-- 保持 -->
      <circle cx="880" cy="720" r="3" fill="#9C27B0"/>  <!-- 变异 -->
      
      <!-- 传送带箭头 -->
      <path d="M 320 720 L 340 720" stroke="white" stroke-width="3" marker-end="url(#whitearrow)"/>
      <path d="M 490 720 L 510 720" stroke="white" stroke-width="3" marker-end="url(#whitearrow)"/>
      <path d="M 690 720 L 710 720" stroke="white" stroke-width="3" marker-end="url(#whitearrow)"/>
    </g>
    
    <!-- 变异策略展示 -->
    <g id="mutation-strategies">
      <rect x="1000" y="690" width="280" height="80" rx="10" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="2"/>
      <text x="1140" y="710" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
        🧬 变异策略库
      </text>
      
      <text x="1020" y="730" font-family="Arial, sans-serif" font-size="10" fill="white">• DE/best/1: 精英引导</text>
      <text x="1020" y="745" font-family="Arial, sans-serif" font-size="10" fill="white">• DE/current-to-best/1: 局部优化</text>
      <text x="1020" y="760" font-family="Arial, sans-serif" font-size="10" fill="white">• DE/rand/2: 随机探索</text>
    </g>
  </g>
  
  <!-- 输出箭头 -->
  <path d="M 700 860 Q 720 880 700 900" stroke="#333" stroke-width="4" fill="none" marker-end="url(#flowArrow)"/>
  <text x="700" y="920" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#333">
    🔄 继续流程 → 交叉融合 + 选择淘汰 + 局部精炼
  </text>
  
  <!-- 定义箭头标记 -->
  <defs>
    <marker id="flowArrow" markerWidth="12" markerHeight="8" 
     refX="10" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#333" />
    </marker>
    <marker id="whitearrow" markerWidth="10" markerHeight="7" 
     refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="white" />
    </marker>
  </defs>
</svg>
