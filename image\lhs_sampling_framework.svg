<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义渐变色 -->
    <linearGradient id="lhsGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#81C784;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="processGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2196F3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#64B5F6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cubeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF9800;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFB74D;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="supplementGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9C27B0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#BA68C8;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1400" height="900" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="700" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#333">
    基于网络直径的LHS采样框架 - 动态维度分析
  </text>
  
  <!-- 第一部分：网络区域划分 -->
  <g id="network-division">
    <rect x="50" y="70" width="400" height="150" rx="10" fill="#E3F2FD" stroke="#1976D2" stroke-width="2"/>
    <text x="250" y="95" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#1976D2">
      步骤1: 网络区域划分
    </text>
    
    <!-- 网络示意图 -->
    <g id="network-regions">
      <!-- 直径路径 R0 -->
      <circle cx="100" cy="130" r="8" fill="#D32F2F"/>
      <circle cx="130" cy="130" r="8" fill="#D32F2F"/>
      <circle cx="160" cy="130" r="8" fill="#D32F2F"/>
      <circle cx="190" cy="130" r="8" fill="#D32F2F"/>
      <circle cx="220" cy="130" r="8" fill="#D32F2F"/>
      <line x1="100" y1="130" x2="220" y2="130" stroke="#D32F2F" stroke-width="3"/>
      <text x="160" y="150" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#D32F2F">
        R0: 直径路径 (5个节点)
      </text>
      
      <!-- 第一层 R1 -->
      <circle cx="100" cy="170" r="6" fill="#FF9800"/>
      <circle cx="130" cy="170" r="6" fill="#FF9800"/>
      <circle cx="160" cy="170" r="6" fill="#FF9800"/>
      <circle cx="190" cy="170" r="6" fill="#FF9800"/>
      <circle cx="220" cy="170" r="6" fill="#FF9800"/>
      <text x="160" y="190" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#FF9800">
        R1: 距离=1 (5个节点)
      </text>
      
      <!-- 第二层 R2 -->
      <circle cx="80" cy="200" r="4" fill="#4CAF50"/>
      <circle cx="110" cy="200" r="4" fill="#4CAF50"/>
      <circle cx="140" cy="200" r="4" fill="#4CAF50"/>
      <circle cx="170" cy="200" r="4" fill="#4CAF50"/>
      <circle cx="200" cy="200" r="4" fill="#4CAF50"/>
      <circle cx="230" cy="200" r="4" fill="#4CAF50"/>
      <circle cx="260" cy="200" r="4" fill="#4CAF50"/>
      <circle cx="290" cy="200" r="4" fill="#4CAF50"/>
      <circle cx="320" cy="200" r="4" fill="#4CAF50"/>
      <circle cx="350" cy="200" r="4" fill="#4CAF50"/>
      <text x="215" y="220" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#4CAF50">
        R2: 距离=2 (10个节点)
      </text>
    </g>
  </g>
  
  <!-- 第二部分：维度计算 -->
  <g id="dimension-calculation">
    <rect x="500" y="70" width="350" height="150" rx="10" fill="#FFF3E0" stroke="#F57C00" stroke-width="2"/>
    <text x="675" y="95" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#F57C00">
      步骤2: 动态维度计算
    </text>
    
    <text x="520" y="120" font-family="Arial, sans-serif" font-size="14" fill="#333">
      effective_dims = min(k, 区域数)
    </text>
    
    <!-- 三种情况示例 -->
    <text x="520" y="145" font-family="Arial, sans-serif" font-size="12" fill="#333">
      情况1: k=3, 区域数=3 → effective_dims=3 (3维)
    </text>
    <text x="520" y="165" font-family="Arial, sans-serif" font-size="12" fill="#333">
      情况2: k=3, 区域数=2 → effective_dims=2 (2维)
    </text>
    <text x="520" y="185" font-family="Arial, sans-serif" font-size="12" fill="#333">
      情况3: k=2, 区域数=5 → effective_dims=2 (2维)
    </text>
    
    <text x="675" y="210" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#666">
      维度数决定了LHS采样空间的几何形状
    </text>
  </g>
  
  <!-- 第三部分：LHS采样过程 -->
  <g id="lhs-process">
    <rect x="900" y="70" width="450" height="150" rx="10" fill="url(#lhsGradient)" stroke="#4CAF50" stroke-width="2"/>
    <text x="1125" y="95" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
      步骤3: LHS逐个采样过程
    </text>
    
    <text x="920" y="120" font-family="Arial, sans-serif" font-size="12" fill="white">
      for i in range(SN):  # 生成SN个解
    </text>
    <text x="940" y="140" font-family="Arial, sans-serif" font-size="12" fill="white">
      lhs_sample = lhs(effective_dims, samples=1)[0]
    </text>
    <text x="940" y="160" font-family="Arial, sans-serif" font-size="12" fill="white">
      # 生成1×effective_dims的向量
    </text>
    <text x="940" y="180" font-family="Arial, sans-serif" font-size="12" fill="white">
      for j in range(effective_dims):
    </text>
    <text x="960" y="200" font-family="Arial, sans-serif" font-size="12" fill="white">
      从区域Rj中采样一个节点
    </text>
  </g>
  
  <!-- 箭头 -->
  <path d="M 250 230 L 250 260" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 675 230 L 675 260" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 1125 230 L 1125 260" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 第四部分：三种维度情况的可视化 -->
  <g id="dimension-visualization">
    <!-- 2维情况 -->
    <g id="2d-case">
      <rect x="50" y="280" width="300" height="200" rx="10" fill="#E8F5E8" stroke="#388E3C" stroke-width="2"/>
      <text x="200" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#388E3C">
        2维情况 (effective_dims=2)
      </text>
      
      <!-- 2D正方形 -->
      <rect x="120" y="330" width="80" height="80" fill="url(#cubeGradient)" stroke="#FF5722" stroke-width="2"/>
      <text x="160" y="325" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">R0</text>
      <text x="105" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">R1</text>
      
      <!-- 采样点 -->
      <circle cx="140" cy="350" r="3" fill="#D32F2F"/>
      <circle cx="180" cy="390" r="3" fill="#D32F2F"/>
      <circle cx="160" cy="370" r="3" fill="#D32F2F"/>
      
      <text x="200" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">
        LHS向量: [x, y]
      </text>
      <text x="200" y="450" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">
        从R0和R1各选1个节点
      </text>
      <text x="200" y="470" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">
        需要补充到k个节点
      </text>
    </g>
    
    <!-- 3维情况 -->
    <g id="3d-case">
      <rect x="400" y="280" width="300" height="200" rx="10" fill="#E3F2FD" stroke="#1976D2" stroke-width="2"/>
      <text x="550" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#1976D2">
        3维情况 (effective_dims=3)
      </text>
      
      <!-- 3D立方体 -->
      <rect x="470" y="350" width="60" height="60" fill="url(#cubeGradient)" stroke="#FF5722" stroke-width="2"/>
      <polygon points="470,350 490,330 550,330 530,350" fill="#FFB74D" stroke="#FF5722" stroke-width="2"/>
      <polygon points="530,350 550,330 550,390 530,410" fill="#FF8A65" stroke="#FF5722" stroke-width="2"/>
      
      <text x="485" y="325" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#333">R0</text>
      <text x="455" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#333">R1</text>
      <text x="565" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#333">R2</text>
      
      <!-- 采样点 -->
      <circle cx="485" cy="365" r="3" fill="#D32F2F"/>
      <circle cx="505" cy="385" r="3" fill="#D32F2F"/>
      <circle cx="515" cy="370" r="3" fill="#D32F2F"/>
      
      <text x="550" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">
        LHS向量: [x, y, z]
      </text>
      <text x="550" y="450" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">
        从R0,R1,R2各选1个节点
      </text>
      <text x="550" y="470" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">
        正好k=3个节点，无需补充
      </text>
    </g>
    
    <!-- 高维情况 -->
    <g id="high-d-case">
      <rect x="750" y="280" width="300" height="200" rx="10" fill="#F3E5F5" stroke="#9C27B0" stroke-width="2"/>
      <text x="900" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#9C27B0">
        高维情况 (effective_dims>3)
      </text>
      
      <!-- 多维表示 -->
      <rect x="820" y="330" width="40" height="40" fill="url(#cubeGradient)" stroke="#FF5722" stroke-width="2"/>
      <rect x="830" y="320" width="40" height="40" fill="url(#cubeGradient)" stroke="#FF5722" stroke-width="1" opacity="0.7"/>
      <rect x="840" y="310" width="40" height="40" fill="url(#cubeGradient)" stroke="#FF5722" stroke-width="1" opacity="0.5"/>
      <rect x="850" y="300" width="40" height="40" fill="url(#cubeGradient)" stroke="#FF5722" stroke-width="1" opacity="0.3"/>
      
      <text x="900" y="390" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">
        LHS向量: [x₁, x₂, ..., xₙ]
      </text>
      <text x="900" y="410" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">
        从n个区域各选1个节点
      </text>
      <text x="900" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#333">
        n = min(k, 区域数)
      </text>
      <text x="900" y="450" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">
        可能需要补充策略
      </text>
    </g>
  </g>
  
  <!-- 第五部分：补充策略 -->
  <g id="supplement-strategy">
    <rect x="1100" y="280" width="250" height="200" rx="10" fill="url(#supplementGradient)" stroke="#9C27B0" stroke-width="2"/>
    <text x="1225" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">
      补充策略
    </text>
    
    <text x="1120" y="330" font-family="Arial, sans-serif" font-size="11" fill="white">
      当 len(solution) &lt; k 时:
    </text>
    
    <rect x="1120" y="340" width="200" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="1220" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
      阶段1: 区域轮询补充
    </text>
    
    <rect x="1120" y="380" width="200" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="1220" y="400" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
      阶段2: 桥节点补充
    </text>
    
    <rect x="1120" y="420" width="200" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
    <text x="1220" y="440" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
      阶段3: 外围节点补充
    </text>
    
    <text x="1225" y="470" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">
      确保最终解大小 = k
    </text>
  </g>
  
  <!-- 箭头 -->
  <path d="M 200 490 L 200 520" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 550 490 L 550 520" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 900 490 L 900 520" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 第六部分：关键洞察 -->
  <g id="key-insights">
    <rect x="50" y="540" width="1300" height="320" rx="10" fill="#FAFAFA" stroke="#666" stroke-width="2"/>
    <text x="700" y="570" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#333">
      关键洞察与代码分析
    </text>
    
    <!-- 洞察1 -->
    <rect x="80" y="590" width="380" height="120" rx="5" fill="#E3F2FD" stroke="#1976D2" stroke-width="1"/>
    <text x="270" y="615" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#1976D2">
      1. LHS不是矩阵，而是逐个向量
    </text>
    <text x="90" y="635" font-family="Arial, sans-serif" font-size="11" fill="#333">
      • lhs(effective_dims, samples=1)[0] 每次生成1个向量
    </text>
    <text x="90" y="650" font-family="Arial, sans-serif" font-size="11" fill="#333">
      • 不是一次性生成n×effective_dims矩阵
    </text>
    <text x="90" y="665" font-family="Arial, sans-serif" font-size="11" fill="#333">
      • 而是循环SN次，每次生成1×effective_dims向量
    </text>
    <text x="90" y="680" font-family="Arial, sans-serif" font-size="11" fill="#333">
      • 每个向量对应一个候选解的采样过程
    </text>
    <text x="90" y="695" font-family="Arial, sans-serif" font-size="10" fill="#666">
      代码: for _ in range(SN): lhs_sample = lhs(...)
    </text>
    
    <!-- 洞察2 -->
    <rect x="480" y="590" width="380" height="120" rx="5" fill="#E8F5E8" stroke="#388E3C" stroke-width="1"/>
    <text x="670" y="615" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#388E3C">
      2. 维度数是动态的
    </text>
    <text x="490" y="635" font-family="Arial, sans-serif" font-size="11" fill="#333">
      • effective_dims = min(k, len(region_keys))
    </text>
    <text x="490" y="650" font-family="Arial, sans-serif" font-size="11" fill="#333">
      • 当k=3, 区域数=3时，是3维立方体
    </text>
    <text x="490" y="665" font-family="Arial, sans-serif" font-size="11" fill="#333">
      • 当k=3, 区域数=2时，是2维正方形
    </text>
    <text x="490" y="680" font-family="Arial, sans-serif" font-size="11" fill="#333">
      • 当k=2, 区域数=5时，是2维正方形
    </text>
    <text x="490" y="695" font-family="Arial, sans-serif" font-size="10" fill="#666">
      几何形状取决于具体的k值和网络结构
    </text>
    
    <!-- 洞察3 -->
    <rect x="880" y="590" width="380" height="120" rx="5" fill="#FFF3E0" stroke="#F57C00" stroke-width="1"/>
    <text x="1070" y="615" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#F57C00">
      3. 补充策略的必要性
    </text>
    <text x="890" y="635" font-family="Arial, sans-serif" font-size="11" fill="#333">
      • LHS只从effective_dims个区域采样
    </text>
    <text x="890" y="650" font-family="Arial, sans-serif" font-size="11" fill="#333">
      • 当effective_dims &lt; k时，解不完整
    </text>
    <text x="890" y="665" font-family="Arial, sans-serif" font-size="11" fill="#333">
      • 需要三阶段补充策略填充到k个节点
    </text>
    <text x="890" y="680" font-family="Arial, sans-serif" font-size="11" fill="#333">
      • 保证最终每个解都有k个节点
    </text>
    <text x="890" y="695" font-family="Arial, sans-serif" font-size="10" fill="#666">
      代码: if len(solution) &lt; k: supplement = ...
    </text>
    
    <!-- 具体示例 -->
    <rect x="80" y="730" width="1180" height="120" rx="5" fill="#FFEBEE" stroke="#D32F2F" stroke-width="1"/>
    <text x="670" y="755" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#D32F2F">
      具体示例：k=3, 区域数=3的情况
    </text>
    
    <text x="90" y="780" font-family="Arial, sans-serif" font-size="11" fill="#333">
      1. effective_dims = min(3, 3) = 3 → 使用3维立方体采样
    </text>
    <text x="90" y="800" font-family="Arial, sans-serif" font-size="11" fill="#333">
      2. lhs(3, samples=1) → 生成向量如 [0.2, 0.6, 0.1]
    </text>
    <text x="90" y="820" font-family="Arial, sans-serif" font-size="11" fill="#333">
      3. 从R0选节点2, 从R1选节点9, 从R2选节点11 → 解{2,9,11}
    </text>
    <text x="90" y="840" font-family="Arial, sans-serif" font-size="11" fill="#333">
      4. len(solution)=3=k，无需补充，直接返回完整解
    </text>
    
    <text x="700" y="780" font-family="Arial, sans-serif" font-size="11" fill="#333">
      反例：k=5, 区域数=3的情况
    </text>
    <text x="700" y="800" font-family="Arial, sans-serif" font-size="11" fill="#333">
      1. effective_dims = min(5, 3) = 3 → 仍是3维立方体
    </text>
    <text x="700" y="820" font-family="Arial, sans-serif" font-size="11" fill="#333">
      2. LHS只能从3个区域各选1个节点 → 解只有3个节点
    </text>
    <text x="700" y="840" font-family="Arial, sans-serif" font-size="11" fill="#333">
      3. len(solution)=3&lt;5，需要补充策略添加2个节点
    </text>
  </g>
  
  <!-- 定义箭头标记 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
     refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
</svg>
